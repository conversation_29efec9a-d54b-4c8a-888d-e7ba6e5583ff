apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    appm: {{ .Release.Name }}
  name: &msName {{ include "oki.fullname" . }}-deploy
spec:
  # 云原生应用 弹缩最大最小副本数
  replicas: {{ .Values.replicas.project_manager.init }}
  selector:
    matchLabels:
      name: {{ .Release.Name }}
  strategy:
    type: RollingUpdate
    rollingUpdate: 
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      # 云原生应用 挂载网络
      annotations:
      {{- toYaml .Values.annotations.project_manager | nindent 8 }}
      labels:
        name: {{ .Release.Name }}
    spec:
      # 云原生应用 labelselector
      {{- if .Values.global.INIT_CONTAINER_ENABLED }}
      initContainers:
      - name: &serverInitContainerName {{ .Release.Name }}-init-nonroot
        # 云原生应用 镜像规范
        {{- if .Values.serviceImage.tenant }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- else }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- end }}
        imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
        securityContext:
          privileged: false
          allowPrivilegeEscalation: false
        tty: false
        stdin: false
        command: [ "/home/<USER>/chmod_file.sh" ]
        env:
          - name: permit_root_start
            value: {{ .Values.global.permitRootStart | quote }}
          - name: pvc_type
            value: {{ .Values.global.pvcType | quote }}
          - name: openpalette_ms_bpname
            value: *msName
          - name: openpalette_container_name
            value: *serverInitContainerName
          {{- range .Values.envs.init }}
            {{- include "oki.containerEnvs" . | nindent 10 }}
          {{- end }}
        # 云原生应用 volume 定义和使用
        volumeMounts:
          {{- range .Values.volumes.project_manager }}
          - name: {{ .name}}
            mountPath: {{ .mountPath}}
            readOnly: {{ .readOnly}}
            subPath: {{ .subPath }}
          {{- end }}
        # 云原生应用 容器资源定义
        {{- if .Values.resources.init }}
        resources: {{- toYaml .Values.resources.init | nindent 10 }}
        {{- end }}
      {{- end }}        
      containers:
      - name: &serverContainerName {{ .Release.Name }}
        env:
          # 微服务自己的部署参数
          # 云原生应用 global 属性  --- 注入公共属性和 msb信息
        - name: TZ
          value: {{ .Values.global.TZ }}
        - name: OPENPALETTE_MSB_IP
          value: {{ .Values.global.OPENPALETTE_MSB_IP }}
        - name: OPENPALETTE_MSB_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_PORT | quote }}
        - name: OPENPALETTE_MSB_ROUTER_IP
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_IP }}
        - name: OPENPALETTE_MSB_ROUTER_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT | quote }}   
        - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT | quote }}     
        - name: OPENPALETTE_NAMESPACE
          value: {{ .Values.global.OPENPALETTE_NAMESPACE }}
        # 云原生应用使用 使用第三方公共服务  方式一； 同时也采用方式二配置了
        # kafka配置
        - name: OPENPALETTE_KAFKA_ADDRESS
          value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_ADDRESS | quote }}
        - name: OPENPALETTE_KAFKA_PORT
          value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_PORT | quote }}
        - name: OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS
          value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS | quote }}
        - name: OPENPALETTE_KAFKA_ZOOKEEPER_PORT
          value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_ZOOKEEPER_PORT | quote }}
        #redis配置
        - name: OPENPALETTE_REDIS_ADDRESS
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_ADDRESS }}
        - name: OPENPALETTE_REDIS_PORT
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PORT | quote }}
        - name: OPENPALETTE_REDIS_PASSWORD
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PASSWORD }}
        - name: OPENPALETTE_REDIS_SENTINEL_ADDRESS
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_ADDRESS | quote }}
        - name: OPENPALETTE_REDIS_SENTINEL_PORT
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_PORT | quote }}
        - name: OPENPALETTE_REDIS_SENTINEL_MASTERNAME
          value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_MASTERNAME | quote }}
          #pg配置
        - name: OPENPALETTE_PG_ADDRESS
          value: {{ .Values.pgConfig.OPENPALETTE_PG_ADDRESS }}
        - name: OPENPALETTE_PG_PORT
          value: {{ .Values.pgConfig.OPENPALETTE_PG_PORT | quote }}
        - name: OPENPALETTE_PG_DBNAME
          value: {{ .Values.pgConfig.OPENPALETTE_PG_DBNAME }}
        - name: OPENPALETTE_PG_USERNAME
          value: {{ .Values.pgConfig.OPENPALETTE_PG_USERNAME }}
        - name: OPENPALETTE_PG_PASSWORD
          value: {{ .Values.pgConfig.OPENPALETTE_PG_PASSWORD }}
        # zk
        - name: OPENPALETTE_ZOOKEEPER_ADDRESS
          value: {{ .Values.zkConfig.OPENPALETTE_ZOOKEEPER_ADDRESS }}
        - name: OPENPALETTE_ZOOKEEPER_PORT
          value: {{ .Values.zkConfig.OPENPALETTE_ZOOKEEPER_PORT | quote }}
        - name: OPENPALETTE_ZOOKEEPER_AUTH_SCHEME
          value: {{ .Values.zkConfig.OPENPALETTE_ZOOKEEPER_AUTH_SCHEME }}
        - name: OPENPALETTE_ZOOKEEPER_AUTH_INFO
          value: {{ .Values.zkConfig.OPENPALETTE_ZOOKEEPER_AUTH_INFO }}
        - name: openpalette_ms_bpname
          value: *msName
        - name: openpalette_container_name
          value: *serverContainerName
        {{- range .Values.envs.project_manager }}
          {{- include "oki.containerEnvs" . | nindent 8 }}
        {{- end }}          
        # 云原生应用 镜像规范
        {{- if .Values.serviceImage.tenant }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- else }}
        image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.name }}:{{ .Values.serviceImage.version }}
        {{- end }}
        imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
        # 云原生应用 安全上下文
        securityContext:
        {{- toYaml .Values.securityContext | nindent 10 }}
        ports:
        - containerPort: {{ .Values.ports.project_manager_29136 }}
          protocol: TCP
        # 云原生应用 容器资源定义
        {{- if .Values.resources.project_manager }}
        resources: {{- toYaml .Values.resources.project_manager | nindent 10 }}
        {{- end }}
        # 云原生应用 volume 定义和使用
        volumeMounts:
          {{- range .Values.volumes.project_manager }}
          - name: {{ .name}}
            mountPath: {{ .mountPath}}
            readOnly: {{ .readOnly}}
            subPath: {{ .subPath }}
          {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}      
      # 云原生应用 volume 定义和使用
      volumes:
        {{- range .Values.volumes.project_manager }}
        - name: {{ .name}}
          {{ .volumeType }}:
          {{ .volumeKey | indent 2 }}: {{ .volumeValue }}
          {{- if or (eq .volumeType "configMap") (eq .volumeType "secret") }}
            defaultMode: {{ .defaultMode }}
          {{- end }}
        {{- end }}