{"scale": "scale_ha", "description": "imop resource ha.", "pvc_params": [{"name": "data-gr", "value": "1Gi", "cluster_name": "default1", "glusterfs_cluster_name": "default1", "path": "/NFS_DIR/share0", "product_id": "gr"}, {"name": "zae-bs-madata", "value": "10Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "nae-commoncomponent-data", "value": "10Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "zae-bs-promptadapter-data", "value": "10Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "zae-chatbot-data", "value": "1Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "home-intelligent-assistants", "value": "1Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "home-zae-ragevaluation", "value": "10Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "home-knowledge-plugins", "value": "20Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "data-gr", "value": "20Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "zae-apimappingservice-data", "value": "5Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "zae-zagents-data", "value": "20Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "nae-ifactory-agent-run-data", "value": "10Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}, {"name": "aab-ragdata", "value": "50Gi", "cluster_name": "nfs", "glusterfs_cluster_name": "default1", "path": "/paasdata/NFS_DIR"}, {"name": "evaluation-data", "value": "10Gi", "cluster_name": "nfs", "path": "/paasdata/NFS_DIR", "glusterfs_cluster_name": "default1"}], "commonservice": [{"service_name": "elasticsearch-543", "service_name_bp": "ES-5.4.3", "orch_name": "commsrv_elasticsearch_vnpm_bp", "service_description": "elasticsearch", "parameters": [{"name": "rc_num", "value": "3"}, {"name": "cpus_limit", "value": "2"}, {"name": "memory_limit", "value": "6Gi"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "PVC_SIZE", "value": "5Gi"}, {"name": "GATE", "value": "true"}, {"name": "label", "value": "otcp_es_cluster1"}], "instances": [{"name": "zenap_elasticsearch", "resource_description": "elasticsearch"}], "volume_info": {"pvc_list": [{"pvc_path": "/NFS_DIR/share0", "pvc_capacity": "50Gi", "cluster_name": "default1", "glusterfs_cluster_name": "default1", "volume_name": "sharepvc", "pvc_name": "elasticsearch"}, {"pvc_capacity": "50Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "data", "pvc_name": "elasticsearchStorage"}]}, "disk_threshold": "100Gi"}, {"disployed_tenants": ["openpalette-extention", "uedm"], "disk_threshold": "100Gi", "parameters": [{"name": "rc_num", "value": "1"}, {"name": "cpus_limit", "value": "4"}, {"name": "memory_limit", "value": "6Gi"}, {"name": "label", "value": "RAN_es_cluster1"}, {"name": "label", "value": "cs_service"}, {"name": "PVC_SIZE", "value": "100Gi"}, {"name": "cpus_request", "value": "0.1"}, {"name": "memory_request", "value": "6Gi"}, {"name": "node-disperse-strategy", "value": "on"}, {"name": "SFTP_BACKUP_TIMEOUT", "value": "24"}, {"name": "DR_SNAPSHOT_TIMEOUT", "value": "1440", "upgrade": "true"}], "orch_name": "commsrv_elasticsearch_vnpm_bp", "volume_info": {"pvc_list": [{"pvc_path": "/paasdata/NFS_DIR", "pvc_capacity": "100Gi", "cluster_name": "nfs", "volume_name": "sharepvc", "pvc_name": "elasticsearch-543", "tenant": "uedm", "glusterfs_cluster_name": "default1"}], "disk_threshold": "100Gi"}, "instances": [{"name": "zae_rag_elasticsearch", "username": "<PERSON><PERSON><PERSON>", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}, {"name": "aab_kbase_elasticsearch", "username": "aabkbase", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}, {"name": "prompt_adapter_elasticsearch", "username": "es73c8LJ", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}], "service_name_bp": "ES-5.4.3", "service_description": "{'CN': 'hi-ran智能节能系统日志', 'EN': 'Hi-ran Energy Saving System Logs.'}", "service_name": "zaerag-elasticsearch-543"}, {"service_name": "kafka", "service_name_bp": "Apache-Kafka", "orch_name": "commsrv_kafka_vnpm_bp", "service_description": "kafka", "parameters": [{"name": "KAFKA_RC_NUM", "value": "3"}, {"name": "ZK_RC_NUM", "value": "3"}, {"name": "KAFKA_CPUS", "value": "6"}, {"name": "ZK_CPUS", "value": "2"}, {"name": "KAFKA_MEMORY", "value": "6Gi"}, {"name": "ZK_MEMORY", "value": "2Gi"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "ZK_CONNECTION_TIMEOUT_MS", "value": "60000"}, {"name": "ZK_SESSION_TIMEOUT_MS", "value": "30000"}, {"name": "LOG_RETENTION_BYTES", "value": "104857600"}, {"name": "LOG_SEGMENT_BYTES", "value": "104857600"}, {"name": "LOG_RETENTION_HOURS", "value": "168"}, {"name": "LOG_FILE_COUNT", "value": "10"}, {"name": "LOG_FILE_SIZE", "value": "100M"}, {"name": "KAFKA_AUTO_CREATE_TOPICS_ENABLE", "value": "true"}, {"name": "KAFKA_UNCLEAN_LEADER_ELECTION_ENABLE", "value": "true"}, {"name": "KAFKA_NUM_IO_THREADS", "value": "8"}, {"name": "KAFKA_NUM_NETWORK_THREADS", "value": "6"}, {"name": "KAFKA_QUEUED_MAX_REQUESTS", "value": "1000"}, {"name": "UNDER_REPLICA_PARTITION_PERCENT", "value": "0.1"}, {"name": "label", "value": "otcp_kafka_cluster1"}], "instances": [{"name": "zenap_kafka", "resource_description": "kafka"}], "volume_info": {"pvc_list": [{"pvc_capacity": "25Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "zookeeper.zk-data", "pvc_name": "kafkaZK"}, {"pvc_capacity": "25Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "kafka.broker-data", "pvc_name": "kafka"}]}, "disk_threshold": "50Gi"}, {"service_name": "logstash", "service_name_bp": "Logstash-5.4.3", "orch_name": "commsrv_logstash_vnpm_bp", "service_description": "logstash", "parameters": [{"name": "rc_num", "value": "3"}, {"name": "cpus_limit", "value": "2"}, {"name": "memory_limit", "value": "1Gi"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "Apache-Kafka", "value": "kafka"}, {"name": "ES-5.4.3", "value": ""}, {"name": "label", "value": "otcp_logstash_cluster1"}], "instances": [{"name": "zenap_logstash", "resource_description": "logstash"}], "disk_threshold": "100Gi"}, {"service_name": "zookeeper", "service_name_bp": "Zookeeper-vnpm", "orch_name": "commsrv_zookeeper_vnpm_bp", "service_description": "zookeeper", "parameters": [{"name": "rc_num", "value": "3"}, {"name": "cpus_limit", "value": "4"}, {"name": "memory_limit", "value": "4Gi"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "label", "value": "otcp_zk_cluster1"}], "instances": [{"name": "zenap_zookeeper", "resource_description": "zookeeper"}], "volume_info": {"pvc_list": [{"pvc_path": "/NFS_DIR/share0", "pvc_capacity": "50Gi", "cluster_name": "default1", "glusterfs_cluster_name": "default1", "volume_name": "sharepvc", "pvc_name": "zookeeper"}]}, "disk_threshold": "50Gi"}, {"service_name": "ftp-sftp-ftps", "service_name_bp": "FTPv3", "orch_name": "commsrv_zenap_ftpv3_bp", "service_description": "ftp-sftp-ftps", "port_preset": {"ZENAP_FTP_PORT_PUB": "", "ZENAP_SFTP_PORT_PUB": ""}, "parameters": [{"name": "rc_num", "value": "1"}, {"name": "cpus_limit", "value": "2"}, {"name": "memory_limit", "value": "4Gi"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "ftp_visualRange", "value": "1"}, {"name": "ftp_data_capacity", "value": "10Gi"}, {"name": "sftp_moduliSize", "value": "2048-8192"}, {"name": "sftp_keyexchange", "value": "DH<PERSON>X256,<PERSON><PERSON><PERSON>256,<PERSON>DHP384"}, {"name": "sftp_ciphersuite", "value": "AES128CTR,AES192CTR,AES256CTR"}, {"name": "sftp_macs", "value": "HMACSHA1"}, {"name": "ftps_protocols", "value": "TLSv1,TLSv1.1,TLSv1.2"}, {"name": "ftps_ciphersuite", "value": "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA"}, {"name": "ftp_publish_ip", "value": ""}, {"name": "instance_label_if_slb", "value": "net_ne"}, {"name": "label", "value": "otcp_ftp_cluster1"}, {"name": "sftp_hostkey", "value": "ECDSA256,ECDSA384,ECDSA521"}], "instances": [], "volume_info": {"pvc_list": [{"pvc_path": "/NFS_DIR/share0", "pvc_capacity": "50Gi", "cluster_name": "default1", "glusterfs_cluster_name": "default1", "volume_name": "sharepvc", "pvc_name": "ftp-sftp-ftps"}, {"volume_name": "mngdatas", "pvc_name": "ftp-mngdatas", "cluster_name": "default1", "pvc_path": "/NFS_DIR/share0", "glusterfs_cluster_name": "default1", "pvc_capacity": "50Gi"}]}, "disk_threshold": "100Gi"}, {"service_name": "postgresql", "service_name_bp": "PostgreSQL_VNPM", "orch_name": "commsrv_pg_vnpm_bp", "service_description": "postgresql", "port_list": [{"port_name": "SERVICE_ACCESS_PORT_V2", "need_reg": "0"}], "parameters": [{"name": "rc_num", "value": "3"}, {"name": "cpus_limit", "value": "6"}, {"name": "memory_limit", "value": "15Gi"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "dynamic_shared_memory_type", "value": "mmap"}, {"name": "<PERSON><PERSON>", "value": "yes"}, {"name": "MAX_CONNECTIONS", "value": "2048"}, {"name": "SHARED_BUFFERS", "value": "6144MB"}, {"name": "WORK_MEM", "value": "6MB"}, {"name": "MAINTENANCE_WORK_MEM", "value": "512MB"}, {"name": "MAX_WAL_SIZE", "value": "32GB"}, {"name": "WAL_KEEP_SEGMENTS", "value": "400"}, {"name": "timezone", "value": "PRC"}, {"name": "log_timezone", "value": "PRC"}, {"name": "log_connections", "value": "off"}, {"name": "log_disconnections", "value": "off"}, {"name": "HUGE_PAGES", "value": "try"}, {"name": "synchronous_commit", "value": "on"}, {"name": "rep_mode", "value": "sync"}, {"name": "postgres_fdw", "value": "false"}, {"name": "pg_pathman", "value": "false"}, {"name": "timescaledb", "value": "false"}, {"name": "uuid-ossp", "value": "false"}, {"name": "revoke_super_privilege", "value": "true"}, {"name": "read_write_separation", "value": "true"}, {"name": "label", "value": "otcp_pg_cluster1"}], "instances": [{"name": "zenap_itmp_pg", "resource_description": "zenap_itmp_pg"}, {"name": "oes-portal-pg", "resource_description": "oes-portal-pg"}, {"name": "zenap_sm_pg", "resource_description": "zenap_sm_pg"}, {"name": "zenap_license_pg", "resource_description": "zenap_license_pg"}, {"name": "zenap_configcenter_pg", "resource_description": "zenap_configcenter_pg"}, {"name": "rca_db", "resource_description": "rca_db"}, {"name": "zenap_fm_pg", "resource_description": "zenap_fm_pg"}, {"name": "fm_db_pg", "resource_description": "fm_db_pg"}, {"name": "oes_rm_pg", "resource_description": "oes_rm_pg"}], "volume_info": {"pvc_list": [{"pvc_capacity": "800Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "commsrvpg.data", "pvc_name": "postgresql"}]}, "disk_threshold": "1024Gi"}, {"service_name": "postgresqlssm", "is_extention": true, "service_name_bp": "PostgreSQL_VNPM", "orch_name": "commsrv_pg_vnpm_bp", "service_description": "postgresqlssm", "port_list": [{"port_name": "SERVICE_ACCESS_PORT_V2", "need_reg": "0"}], "parameters": [{"name": "rc_num", "value": "3"}, {"name": "cpus_limit", "value": "2"}, {"name": "memory_limit", "value": "4Gi"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "dynamic_shared_memory_type", "value": "mmap"}, {"name": "<PERSON><PERSON>", "value": "yes"}, {"name": "MAX_CONNECTIONS", "value": "1024"}, {"name": "SHARED_BUFFERS", "value": "1024MB"}, {"name": "WORK_MEM", "value": "4MB"}, {"name": "MAINTENANCE_WORK_MEM", "value": "256MB"}, {"name": "MAX_WAL_SIZE", "value": "16GB"}, {"name": "WAL_KEEP_SEGMENTS", "value": "40"}, {"name": "timezone", "value": "PRC"}, {"name": "log_timezone", "value": "PRC"}, {"name": "log_connections", "value": "off"}, {"name": "log_disconnections", "value": "off"}, {"name": "HUGE_PAGES", "value": "off"}, {"name": "synchronous_commit", "value": "on"}, {"name": "rep_mode", "value": "sync"}, {"name": "postgres_fdw", "value": "false"}, {"name": "pg_pathman", "value": "false"}, {"name": "timescaledb", "value": "false"}, {"name": "uuid-ossp", "value": "false"}, {"name": "revoke_super_privilege", "value": "true"}, {"name": "label", "value": "otcp_pg_cluster1"}], "instances": [], "volume_info": {"pvc_list": [{"pvc_capacity": "40Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "commsrvpg.data", "pvc_name": "postgresqlssm"}]}, "disk_threshold": "40Gi"}, {"service_name": "postgresqlcache", "service_name_bp": "PostgreSQLCACHE_VNPM", "orch_name": "commsrv_pgcache_vnpm_bp", "service_description": "postgresqlcache", "port_list": [{"port_name": "SERVICE_ACCESS_PORT_V2", "need_reg": "0"}], "parameters": [{"name": "rc_num", "value": "3"}, {"name": "cpus_limit", "value": "2"}, {"name": "memory_limit", "value": "6Gi"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "dynamic_shared_memory_type", "value": "mmap"}, {"name": "<PERSON><PERSON>", "value": "yes"}, {"name": "MAX_CONNECTIONS", "value": "1024"}, {"name": "SHARED_BUFFERS", "value": "1024MB"}, {"name": "WORK_MEM", "value": "4MB"}, {"name": "MAINTENANCE_WORK_MEM", "value": "256MB"}, {"name": "MAX_WAL_SIZE", "value": "16GB"}, {"name": "WAL_KEEP_SEGMENTS", "value": "400"}, {"name": "timezone", "value": "PRC"}, {"name": "log_timezone", "value": "PRC"}, {"name": "log_connections", "value": "off"}, {"name": "log_disconnections", "value": "off"}, {"name": "HUGE_PAGES", "value": "off"}, {"name": "synchronous_commit", "value": "off"}, {"name": "rep_mode", "value": "async"}, {"name": "postgres_fdw", "value": "false"}, {"name": "pg_pathman", "value": "false"}, {"name": "timescaledb", "value": "false"}, {"name": "uuid-ossp", "value": "false"}, {"name": "revoke_super_privilege", "value": "true"}, {"name": "label", "value": "otcp_pgcache_cluster1"}], "instances": [{"name": "zenap_pg_cache", "resource_description": "zenap_pg_cache"}], "volume_info": {"pvc_list": [{"pvc_capacity": "50Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "commsrvpg.data", "pvc_name": "postgresqlcache"}]}, "disk_threshold": "50Gi"}, {"service_name": "redis", "service_name_bp": "Redis-inner-HA", "orch_name": "commsrv_inner_redis_ha_bp", "service_description": "redis", "parameters": [{"name": "REDIS_RC_NUM1", "value": "2"}, {"name": "SENTINEL_RC_NUM", "value": "3"}, {"name": "REDIS_CPU1", "value": "4"}, {"name": "SENTINEL_CPU", "value": "1"}, {"name": "REDIS_MEMORY1", "value": "10Gi"}, {"name": "SENTINEL_MEMORY", "value": "1Gi"}, {"name": "storage", "value": "CreatedPVC"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "IS_PERSISTENCE", "value": "no"}, {"name": "RDB", "value": "yes"}, {"name": "label", "value": "otcp_redis-ha_cluster1"}], "instances": [{"name": "zenap_redis", "password": "2ZQhXJc/Ligf7yETMlTLy6qTiE0PbaDFeDkzmSEovcE=", "resource_description": "zenap_redis"}], "volume_info": {"pvc_list": [{"pvc_capacity": "50Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "rmt.rmtpath", "pvc_name": "redis"}, {"pvc_capacity": "50Gi", "block_volume_type": "storage_for_commonservice", "volume_name": "haredis.redispath", "pvc_name": "redisStorage"}]}, "disk_threshold": "100Gi"}, {"service_name": "uedm_redis1", "service_name_bp": "Redis-inner-Cluster", "orch_name": "commsrv_inner_redis_cluster_bp", "service_description": "uedm_redis1", "parameters": [{"name": "MASTER_RC_NUM1", "value": "3"}, {"name": "REDIS_IO_THREADS", "value": "4"}, {"name": "SLAVE_RC_NUM", "value": "3"}, {"name": "MASTER_CPU", "value": "2"}, {"name": "SLAVE_CPU", "value": "2"}, {"name": "CONTROLLER_CPU", "value": "1"}, {"name": "MASTER_MEMORY", "value": "3Gi"}, {"name": "SLAVE_MEMORY", "value": "3Gi"}, {"name": "CONTROLLER_MEMORY", "value": "1Gi"}, {"name": "MASTER_CPU_REQUEST", "value": "0.1"}, {"name": "SLAVE_CPU_REQUEST", "value": "0.1"}, {"name": "CONTROLLER_CPU_REQUEST", "value": "0.1"}, {"name": "MASTER_MEMORY_REQUEST", "value": "0.1Gi"}, {"name": "SLAVE_MEMORY_REQUEST", "value": "0.1Gi"}, {"name": "CONTROLLER_MEMORY_REQUEST", "value": "0.1Gi"}, {"name": "cs_cipher", "value": "fixedkey"}, {"name": "IS_PERSISTENCE", "value": "no"}, {"name": "RDB", "value": "yes"}, {"name": "label", "value": "uedm_redis-cluster_cluster1"}], "instances": [{"name": "uedm_redis1", "password": "2ZQhXJc/Ligf7yETMlTLy6qTiE0PbaDFeDkzmSEovcE=", "resource_description": "uedm_redis1"}], "volume_info": {"pvc_list": [{"pvc_capacity": "30Gi", "glusterfs_cluster_name": "default1", "volume_name": "master.sharepvc", "pvc_name": "uedmRedisMaster"}, {"pvc_capacity": "30Gi", "glusterfs_cluster_name": "default1", "volume_name": "slave.sharepvc", "pvc_name": "uedmRedisSlave"}, {"pvc_capacity": "5Gi", "glusterfs_cluster_name": "default1", "volume_name": "controller.sharepvc", "pvc_name": "uedmRedisController"}]}, "disk_threshold": "100Gi"}], "external_commonservice": [{"service_name": "gp_zae_rag", "service_description": "gp", "service_name_bp": "AP_external", "orch_name": "commsrv_ap7_bp_external", "parameters": [{"name": "cs_cipher", "value": "fixedkey"}, {"name": "master_label", "value": "HA_uedm_gp_master_cluster1"}, {"name": "segment_label", "value": "HA_uedm_gp_segment_cluster1"}, {"name": "master_dev", "value": "/paasdata/op-tenant/greenplum"}, {"name": "segment_dev", "value": "/paasdata/op-tenant/greenplum"}, {"name": "mirror_type", "value": "1"}, {"name": "segs_per_host", "value": "2"}, {"name": "swap_size", "value": "16"}, {"name": "port_base", "value": "40000"}, {"name": "swap_path", "value": "/paasdata"}, {"name": "gp_java_home", "value": "/paasdata/op-data/openjdk/zxjdk8u352"}, {"name": "install_path", "value": "/usr/local"}, {"name": "enable_pxf", "value": "true"}, {"name": "enable_mr", "value": "true"}, {"name": "enable_gpreinforce", "value": "true"}, {"name": "optimizer", "value": "-v off"}, {"name": "pljava_vmoptions", "value": "-v '-Xmx4G'"}], "instances": [{"name": "ragkb_db", "dbname": "rag_kb_db", "username": "<PERSON><PERSON><PERSON>", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}, {"name": "rag_publish_db", "dbname": "rag_publish_db", "username": "naerag", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}, {"name": "studio_kbase", "dbname": "ai-kbase", "username": "studiorag", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}, {"name": "promptadapter", "dbname": "promptadapter", "username": "promptadapter", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}, {"name": "asset_center", "dbname": "assert-center-kbase", "username": "assertcenter", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}, {"name": "workbridge", "dbname": "workbridge-kbase", "username": "workbridge", "password": "jEwJIj6HJ7bqwCo+7cSYmw=="}]}], "eps_paras": [{"app_name": "uedm-tower", "service_list": [{"service_name": "uedm-tower", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}]}]}]}, {"app_name": "uedm-north", "service_list": [{"service_name": "uedm-north", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}]}]}]}, {"app_name": "uedm-maintenance", "service_list": [{"service_name": "uedm-maintenance", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}]}]}]}, {"app_name": "uedm-entrance", "service_list": [{"service_name": "uedm-entrance", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}]}]}]}, {"app_name": "north-manager", "service_list": [{"service_name": "north-manager", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "north_manager_replicasMax", "value": "1"}, {"name": "north_manager_replicasMin", "value": "1"}, {"name": "north_manager_cpu_request", "value": "5"}, {"name": "north_manager_mem_request", "value": "5Gi"}, {"name": "north_manager_cpu_limit", "value": "5"}, {"name": "north_manager_mem_limit", "value": "5Gi"}]}]}]}, {"app_name": "uedm-north-sxuc-cinterface", "service_list": [{"service_name": "uedm-north-sxuc-cinterface", "ms_list": [{"ms_name": "north-cinterface-sxucc-adapter", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "north_cinterface_sxucc_adapter_replicasMax", "value": "2"}, {"name": "north_cinterface_sxucc_adapter_replicasMin", "value": "2"}, {"name": "north_cinterface_sxucc_adapter_limits_cpu", "value": "10"}, {"name": "north_cinterface_sxucc_adapter_limits_memory", "value": "15Gi"}, {"name": "north_cinterface_sxucc_adapter_requests_memory", "value": "0.2Gi"}, {"name": "north_cinterface_sxucc_adapter_requests_cpu", "value": "4"}]}]}]}, {"app_name": "uedm-north-cinterface", "service_list": [{"service_name": "uedm-north-cinterface", "ms_list": [{"ms_name": "north-cinterface-adapter", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "uedm_north_cinterface_replicasMax", "value": "1"}, {"name": "uedm_north_cinterface_replicasMin", "value": "1"}, {"name": "uedm_north_cinterface_limits_cpu", "value": "10"}, {"name": "uedm_north_cinterface_limits_memory", "value": "15Gi"}, {"name": "uedm_north_cinterface_requests_memory", "value": "0.1Gi"}, {"name": "uedm_north_cinterface_requests_cpu", "value": "0.1"}]}, {"ms_name": "unicomc-adapter", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "uedm_north_cinterface_replicasMax", "value": "1"}, {"name": "uedm_north_cinterface_replicasMin", "value": "1"}, {"name": "uedm_north_cinterface_limits_cpu", "value": "10"}, {"name": "uedm_north_cinterface_limits_memory", "value": "15Gi"}, {"name": "uedm_north_cinterface_requests_memory", "value": "0.1Gi"}, {"name": "uedm_north_cinterface_requests_cpu", "value": "0.1"}]}]}]}, {"app_name": "uedm-pma", "service_list": [{"service_name": "uedm-pma", "ms_list": [{"ms_name": "pma-model", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "pmamodel_pma-model_mem_limit", "value": "8Gi"}, {"name": "pmamodel_pma-model_mem_request", "value": "0.1Gi"}, {"name": "pmamodel_pma-model_cpu_limit", "value": "5"}, {"name": "pmamodel_pma-model_cpu_request", "value": "0.1"}]}, {"ms_name": "pma-collect", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "pmacollect_pma-collect_mem_limit", "value": "8Gi"}, {"name": "pmacollect_pma-collect_mem_request", "value": "0.1Gi"}, {"name": "pmacollect_pma-collect_cpu_limit", "value": "16"}, {"name": "pmacollect_pma-collect_cpu_request", "value": "0.1"}, {"name": "pmacollect_replicas_min", "value": "3"}, {"name": "pmacollect_replicas_max", "value": "3"}]}, {"ms_name": "pma-backup", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "pmabackup_pma-backup_mem_limit", "value": "5Gi"}, {"name": "pmabackup_pma-backup_mem_request", "value": "0.1Gi"}, {"name": "pmabackup_pma-backup_cpu_limit", "value": "5"}, {"name": "pmabackup_pma-backup_cpu_request", "value": "0.1"}]}, {"ms_name": "pma-makeup", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "pmamakeup_pma-makeup_mem_limit", "value": "8Gi"}, {"name": "pmamakeup_pma-makeup_mem_request", "value": "0.1Gi"}, {"name": "pmamakeup_pma-makeup_cpu_limit", "value": "15"}, {"name": "pmamakeup_pma-makeup_cpu_request", "value": "0.1"}]}]}]}, {"app_name": "uedm-alarm", "service_list": [{"service_name": "uedm-alarm", "ms_list": [{"ms_name": "alarm-manager", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "alarmmanager_alarm-manager_mem_limit", "value": "10Gi"}, {"name": "alarmmanager_alarm-manager_mem_request", "value": "0.2Gi"}, {"name": "alarmmanager_alarm-manager_cpu_limit", "value": "4"}, {"name": "alarmmanager_alarm-manager_cpu_request", "value": "0.5"}]}, {"ms_name": "alarm-processor", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "alarmprocessor_alarm-processor_mem_limit", "value": "16Gi"}, {"name": "alarmprocessor_alarm-processor_mem_request", "value": "0.2Gi"}, {"name": "alarmprocessor_alarm-processor_cpu_limit", "value": "12"}, {"name": "alarmprocessor_alarm-processor_cpu_request", "value": "0.5"}]}, {"ms_name": "alarm-iui", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "alarmiui_alarm-iui_mem_limit", "value": "128Mi"}, {"name": "alarmiui_alarm-iui_mem_request", "value": "128Mi"}, {"name": "alarmiui_alarm-iui_cpu_limit", "value": "1"}, {"name": "alarmiui_alarm-iui_cpu_request", "value": "0.1"}]}]}]}, {"app_name": "uedm-mp", "service_list": [{"service_name": "uedm-mp", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "mp_manager_replicasMax", "value": "6"}, {"name": "mp_manager_replicasMin", "value": "1"}, {"name": "mp_manager_limits_cpu", "value": "16"}, {"name": "mp_manager_limits_memory", "value": "10Gi"}, {"name": "mp_manager_requests_memory", "value": "4.0Gi"}, {"name": "mp_manager_requests_cpu", "value": "1"}, {"name": "mp_manager_init_requests_memory", "value": "4Gi"}, {"name": "mp_manager_init_requests_cpu", "value": "1"}, {"name": "mp_manager_init_limits_memory", "value": "10Gi"}, {"name": "mp_manager_init_limits_cpu", "value": "16"}]}]}]}, {"app_name": "south-snmp-adapter", "service_list": [{"service_name": "south-snmp-adapter", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_adapter_snmp_replicasMax", "value": "1"}, {"name": "south_adapter_snmp_replicasMin", "value": "1"}, {"name": "south_adapter_snmp_cpu_request", "value": "0.5"}, {"name": "south_adapter_snmp_mem_request", "value": "0.5Gi"}, {"name": "south_adapter_snmp_cpu_limit", "value": "5.0"}, {"name": "south_adapter_snmp_mem_limit", "value": "4.0Gi"}]}]}]}, {"app_name": "uedm-etl", "service_list": [{"service_name": "uedm-etl", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "etl_manager_replicasMax", "value": "1"}, {"name": "etl_manager_replicasMin", "value": "1"}, {"name": "etl_manager_limits_cpu", "value": "5"}, {"name": "etl_manager_limits_memory", "value": "8Gi"}, {"name": "etl_manager_requests_memory", "value": "0.5Gi"}, {"name": "etl_manager_requests_cpu", "value": "0.5"}, {"name": "etl_manager_init_requests_memory", "value": "0.5Gi"}, {"name": "etl_manager_init_requests_cpu", "value": "0.5"}, {"name": "etl_manager_init_limits_memory", "value": "8.0Gi"}, {"name": "etl_manager_init_limits_cpu", "value": "5"}]}]}]}, {"app_name": "uedm-config", "service_list": [{"service_name": "uedm-config", "ms_list": [{"ms_name": "uedm-config-config-manager", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "config-manager_cpu_request", "value": "4"}, {"name": "config-manager_mem_request", "value": "6Gi"}, {"name": "config-manager_cpu_limit", "value": "4"}, {"name": "config-manager_mem_limit", "value": "6Gi"}, {"name": "config-manager_replica_init", "value": "1"}, {"name": "config-manager_replica_max", "value": "1"}, {"name": "config-manager_replica_min", "value": "1"}]}, {"ms_name": "uedm-config-config-iui", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "config-iui_replica_init", "value": "1"}, {"name": "config-iui_replica_max", "value": "1"}, {"name": "config-iui_replica_min", "value": "1"}, {"name": "config-iui_cpu_request", "value": "0.1"}, {"name": "config-iui_mem_request", "value": "256Mi"}, {"name": "config-iui_cpu_limit", "value": "1"}, {"name": "config-iui_mem_limit", "value": "256Mi"}]}]}]}, {"app_name": "south-modbus-adapter", "service_list": [{"service_name": "south-modbus-adapter", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_modbus_adapter_replicasMax", "value": "1"}, {"name": "south_modbus_adapter_replicasMin", "value": "1"}, {"name": "south_modbus_adapter_cpu_request", "value": "0.1"}, {"name": "south_modbus_adapter_mem_request", "value": "0.1Gi"}, {"name": "south_modbus_adapter_cpu_limit", "value": "3.0"}, {"name": "south_modbus_adapter_mem_limit", "value": "4.0Gi"}]}]}]}, {"app_name": "uedm-configuration", "service_list": [{"service_name": "uedm-configuration", "ms_list": [{"ms_name": "uedm-configuration-cfg-ms", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "cfg-ms_cpu_request", "value": "4"}, {"name": "cfg-ms_mem_request", "value": "6Gi"}, {"name": "cfg-ms_cpu_limit", "value": "4"}, {"name": "cfg-ms_mem_limit", "value": "6Gi"}, {"name": "cfg-ms_replica_init", "value": "1"}, {"name": "cfg-ms_replica_max", "value": "1"}, {"name": "cfg-ms_replica_min", "value": "1"}]}, {"ms_name": "uedm-cfg-cfg-iui", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "cfg-iui_replica_init", "value": "1"}, {"name": "cfg-iui_replica_max", "value": "1"}, {"name": "cfg-iui_replica_min", "value": "1"}, {"name": "cfg-iui_cpu_request", "value": "0.1"}, {"name": "cfg-iui_mem_request", "value": "256Mi"}, {"name": "cfg-iui_cpu_limit", "value": "1"}, {"name": "cfg-iui_mem_limit", "value": "256Mi"}]}]}]}, {"app_name": "uedm-auth-center", "service_list": [{"service_name": "uedm-auth-center", "ms_list": [{"ms_name": "uedm-auth-center-auth-center-ms", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "auth-center-ms_cpu_request", "value": "2"}, {"name": "auth-center-ms_mem_request", "value": "3Gi"}, {"name": "auth-center-ms_cpu_limit", "value": "2"}, {"name": "auth-center-ms_mem_limit", "value": "3Gi"}, {"name": "auth-center-ms_replica_init", "value": "1"}, {"name": "auth-center-ms_replica_max", "value": "1"}, {"name": "auth-center-ms_replica_min", "value": "1"}]}]}]}, {"app_name": "uedm-tenant", "service_list": [{"service_name": "uedm-tenant", "ms_list": [{"ms_name": "uedm-tenant-tenant-manager", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "tenant-manager_cpu_request", "value": "1"}, {"name": "tenant-manager_mem_request", "value": "1Gi"}, {"name": "tenant-manager_cpu_limit", "value": "1"}, {"name": "tenant-manager_mem_limit", "value": "1Gi"}, {"name": "tenant-manager_replica_init", "value": "1"}, {"name": "tenant-manager_replica_max", "value": "1"}, {"name": "tenant-manager_replica_min", "value": "1"}]}, {"ms_name": "uedm-tenant-tenant-iui", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "tenant-iui_replica_init", "value": "1"}, {"name": "tenant-iui_replica_max", "value": "1"}, {"name": "tenant-iui_replica_min", "value": "1"}, {"name": "tenant-iui_cpu_request", "value": "0.05"}, {"name": "tenant-iui_mem_request", "value": "128Mi"}, {"name": "tenant-iui_cpu_limit", "value": "1"}, {"name": "tenant-iui_mem_limit", "value": "128Mi"}]}]}]}, {"app_name": "uedm-asset", "service_list": [{"service_name": "uedm-asset", "ms_list": [{"ms_name": "uedm-asset-asset-manager", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "asset-manager_cpu_request", "value": "4"}, {"name": "asset-manager_mem_request", "value": "4Gi"}, {"name": "asset-manager_cpu_limit", "value": "4"}, {"name": "asset-manager_mem_limit", "value": "4Gi"}, {"name": "asset-manager_replica_init", "value": "1"}, {"name": "asset-manager_replica_max", "value": "1"}, {"name": "asset-manager_replica_min", "value": "1"}]}, {"ms_name": "uedm-asset-asset-iui", "env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "asset-iui_replica_init", "value": "1"}, {"name": "asset-iui_replica_max", "value": "1"}, {"name": "asset-iui_replica_min", "value": "1"}, {"name": "asset-iui_cpu_request", "value": "0.05"}, {"name": "asset-iui_mem_request", "value": "128Mi"}, {"name": "asset-iui_cpu_limit", "value": "1"}, {"name": "asset-iui_mem_limit", "value": "128Mi"}]}]}]}, {"app_name": "uedm-south-bms", "service_list": [{"service_name": "uedm-south-bms", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_bms_adapter_replicasMax", "value": "1"}, {"name": "south_bms_adapter_replicasMin", "value": "1"}, {"name": "south_bms_adapter_limits_cpu", "value": "3"}, {"name": "south_bms_adapter_limits_memory", "value": "4Gi"}, {"name": "south_bms_adapter_requests_memory", "value": "0.1Gi"}, {"name": "south_bms_adapter_requests_cpu", "value": "0.1"}, {"name": "south_bms_adapter_init_limits_cpu", "value": "3"}, {"name": "south_bms_adapter_init_limits_memory", "value": "4Gi"}, {"name": "south_bms_adapter_init_requests_memory", "value": "0.1Gi"}, {"name": "south_bms_adapter_init_requests_cpu", "value": "0.1"}]}]}]}, {"app_name": "battery-manager", "service_list": [{"service_name": "battery-manager", "ms_list": [{"env_list": [], "other_list": [{"name": "battery-manager_replica_max", "type": "default", "value": "1"}, {"name": "battery-manager_replica_min", "type": "default", "value": "1"}, {"name": "battery-manager_replica_init", "type": "default", "value": "1"}, {"name": "battery-manager_hugepage_request", "type": "default", "value": "0"}, {"name": "battery-manager_affinityCPU_request", "type": "default", "value": "0"}, {"name": "battery-manager_mem_request", "type": "default", "value": "0.1Gi"}, {"name": "battery-manager_cpu_request", "type": "default", "value": "0.1"}, {"name": "battery-manager_cpu_limit", "type": "default", "value": "2"}, {"name": "battery-manager_mem_limit", "type": "default", "value": "4Gi"}]}]}]}, {"app_name": "uedm-south-mobilec", "service_list": [{"service_name": "uedm-south-mobilec", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_mobilec_adapter_replicasMax", "value": "1"}, {"name": "south_mobilec_adapter_replicasMin", "value": "1"}, {"name": "south_mobilec_adapter_limits_cpu", "value": "4"}, {"name": "south_mobilec_adapter_limits_memory", "value": "6Gi"}, {"name": "south_mobilec_adapter_requests_memory", "value": "0.1Gi"}, {"name": "south_mobilec_adapter_requests_cpu", "value": "0.1"}, {"name": "south_mobilec_adapter_init_limits_cpu", "value": "4"}, {"name": "south_mobilec_adapter_init_limits_memory", "value": "6Gi"}, {"name": "south_mobilec_adapter_init_requests_memory", "value": "0.1Gi"}, {"name": "south_mobilec_adapter_init_requests_cpu", "value": "0.1"}]}]}]}, {"app_name": "uedm-south-unicomb", "service_list": [{"service_name": "uedm-south-unicomb", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_unicomb_adapter_replicasMax", "value": "2"}, {"name": "south_unicomb_adapter_replicasMin", "value": "1"}, {"name": "south_unicomb_adapter_limits_cpu", "value": "4"}, {"name": "south_unicomb_adapter_limits_memory", "value": "8Gi"}, {"name": "south_unicomb_adapter_requests_memory", "value": "0.1Gi"}, {"name": "south_unicomb_adapter_requests_cpu", "value": "0.1"}, {"name": "south_unicomb_adapter_init_requests_memory", "value": "0.1Gi"}, {"name": "south_unicomb_adapter_init_requests_cpu", "value": "0.1"}, {"name": "south_unicomb_adapter_init_limits_memory", "value": "8.0Gi"}, {"name": "south_unicomb_adapter_init_limits_cpu", "value": "4"}]}]}]}, {"app_name": "uedm-south-telecomb", "service_list": [{"service_name": "uedm-south-telecomb", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_telecomb_adapter_replicasMax", "value": "2"}, {"name": "south_telecomb_adapter_replicasMin", "value": "1"}, {"name": "south_telecomb_adapter_limits_cpu", "value": "4"}, {"name": "south_telecomb_adapter_limits_memory", "value": "8Gi"}, {"name": "south_telecomb_adapter_requests_memory", "value": "0.1Gi"}, {"name": "south_telecomb_adapter_requests_cpu", "value": "0.1"}, {"name": "south_telecomb_adapter_init_requests_memory", "value": "0.1Gi"}, {"name": "south_telecomb_adapter_init_requests_cpu", "value": "0.1"}, {"name": "south_telecomb_adapter_init_limits_memory", "value": "8.0Gi"}, {"name": "south_telecomb_adapter_init_limits_cpu", "value": "4"}]}]}]}, {"app_name": "uedm-north-mqtt", "service_list": [{"service_name": "uedm-north-mqtt", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "north_mqtt_ms_replicasMax", "value": "1"}, {"name": "north_mqtt_ms_replicasMin", "value": "1"}, {"name": "north_mqtt_ms_limits_cpu", "value": "3"}, {"name": "north_mqtt_ms_limits_memory", "value": "8Gi"}, {"name": "north_mqtt_ms_requests_memory", "value": "1Gi"}, {"name": "north_mqtt_ms_requests_cpu", "value": "1"}, {"name": "north_mqtt_ms_init_limits_cpu", "value": "4"}, {"name": "north_mqtt_ms_init_limits_memory", "value": "8Gi"}, {"name": "north_mqtt_ms_init_requests_memory", "value": "1Gi"}, {"name": "north_mqtt_ms_init_requests_cpu", "value": "1"}]}]}]}, {"app_name": "uedm-south-scuc", "service_list": [{"service_name": "uedm-south-scuc", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_scuc_adapter_replicasMax", "value": "1"}, {"name": "south_scuc_adapter_replicasMin", "value": "1"}, {"name": "south_scuc_adapter_limits_cpu", "value": "5"}, {"name": "south_scuc_adapter_limits_memory", "value": "4Gi"}, {"name": "south_scuc_adapter_requests_memory", "value": "0.1Gi"}, {"name": "south_scuc_adapter_requests_cpu", "value": "0.1"}, {"name": "south_scuc_adapter_init_requests_memory", "value": "0.1Gi"}, {"name": "south_scuc_adapter_init_requests_cpu", "value": "0.1"}, {"name": "south_scuc_adapter_init_limits_memory", "value": "4Gi"}, {"name": "south_scuc_adapter_init_limits_cpu", "value": "5"}]}]}]}, {"app_name": "forecast-manage", "service_list": [{"service_name": "forecast-manage", "ms_list": [{"env_list": [], "other_list": [{"name": "forecast-manage_replica_max", "type": "default", "value": "1"}, {"name": "forecast-manage_replica_min", "type": "default", "value": "1"}, {"name": "forecast-manage_replica_init", "type": "default", "value": "1"}, {"name": "forecast-manage_mem_request", "type": "default", "value": "0.1Gi"}, {"name": "forecast-manage_cpu_request", "type": "default", "value": "0.1"}, {"name": "forecast-manage_cpu_limit", "type": "default", "value": "2"}, {"name": "forecast-manage_mem_limit", "type": "default", "value": "4Gi"}]}]}]}, {"app_name": "inspect-manager", "service_list": [{"service_name": "inspect-manager", "ms_list": [{"env_list": [], "other_list": [{"name": "inspect-manager_replica_max", "type": "default", "value": "1"}, {"name": "inspect-manager_replica_min", "type": "default", "value": "1"}, {"name": "inspect-manager_replica_init", "type": "default", "value": "1"}, {"name": "inspect-manager_mem_request", "type": "default", "value": "0.1Gi"}, {"name": "inspect-manager_cpu_request", "type": "default", "value": "0.1"}, {"name": "inspect-manager_cpu_limit", "type": "default", "value": "2"}, {"name": "inspect-manager_mem_limit", "type": "default", "value": "2Gi"}]}]}]}, {"app_name": "efficiency-manager", "service_list": [{"service_name": "efficiency-manager", "ms_list": [{"env_list": [], "other_list": [{"name": "efficiency-manager_replica_max", "type": "default", "value": "1"}, {"name": "efficiency-manager_replica_min", "type": "default", "value": "1"}, {"name": "efficiency-manager_replica_init", "type": "default", "value": "1"}, {"name": "efficiency-manager_mem_request", "type": "default", "value": "0.1Gi"}, {"name": "efficiency-manager_cpu_request", "type": "default", "value": "0.1"}, {"name": "efficiency-manager_cpu_limit", "type": "default", "value": "2"}, {"name": "efficiency-manager_mem_limit", "type": "default", "value": "4Gi"}]}]}]}, {"app_name": "north-manager-snmp", "service_list": [{"service_name": "north-manager-snmp", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "north_manager_snmp_replicasMax", "value": "1"}, {"name": "north_manager_snmp_replicasMin", "value": "1"}, {"name": "north_manager_snmp_cpu_request", "value": "2"}, {"name": "north_manager_snmp_mem_request", "value": "2Gi"}, {"name": "north_manager_snmp_cpu_limit", "value": "12"}, {"name": "north_manager_snmp_mem_limit", "value": "20Gi"}]}]}]}, {"app_name": "south-framework-ms", "service_list": [{"service_name": "south-framework-ms", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_framework_ms_replicasMax", "value": "1"}, {"name": "south_framework_ms_replicasMin", "value": "1"}, {"name": "south_framework_ms_cpu_request", "value": "0.5"}, {"name": "south_framework_ms_mem_request", "value": "0.5Gi"}, {"name": "south_framework_ms_cpu_limit", "value": "3"}, {"name": "south_framework_ms_mem_limit", "value": "4Gi"}]}]}]}, {"app_name": "south-collect-adapter", "service_list": [{"service_name": "south-collect-adapter", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "south_collect_adapter_replicasMax", "value": "5"}, {"name": "south_collect_adapter_replicasMin", "value": "1"}, {"name": "south_collect_adapter_cpu_request", "value": "0.5"}, {"name": "south_collect_adapter_mem_request", "value": "0.5Gi"}, {"name": "south_collect_adapter_cpu_limit", "value": "10"}, {"name": "south_collect_adapter_mem_limit", "value": "10Gi"}]}]}]}, {"app_name": "uedm-avi", "service_list": [{"service_name": "uedm-avi", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "avi-manager_replicas_max", "value": "1"}, {"name": "avi-manager_replicas_min", "value": "1"}, {"name": "avi-manager_cpu_request", "value": "0.5"}, {"name": "avi-manager_mem_request", "value": "0.5Gi"}, {"name": "avi-manager_cpu_limit", "value": "1.0"}, {"name": "avi-manager_mem_limit", "value": "1.0Gi"}]}]}]}, {"app_name": "uedm-battery", "service_list": [{"service_name": "uedm-battery", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "fuelsupply_fuel-supply_replica_max", "value": "6"}, {"name": "fuelsupply_fuel-supply_replica_min", "value": "2"}, {"name": "fuelsupply_fuel-supply_cpu_request", "value": "0.5"}, {"name": "fuelsupply_fuel-supply_mem_request", "value": "0.5Gi"}, {"name": "fuelsupply_fuel-supply_cpu_limit", "value": "3.0"}, {"name": "fuelsupply_fuel-supply_mem_limit", "value": "3.0Gi"}]}]}]}, {"app_name": "uedm-report", "service_list": [{"service_name": "uedm-report", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "report-manager_replicas_max", "value": "6"}, {"name": "report-manager_replicas_min", "value": "2"}, {"name": "report-manager_cpu_request", "value": "0.5"}, {"name": "report-manager_mem_request", "value": "0.5Gi"}, {"name": "report-manager_cpu_limit", "value": "2.0"}, {"name": "report-manager_mem_limit", "value": "3.0Gi"}]}]}]}, {"app_name": "uedm-component", "service_list": [{"service_name": "uedm-component", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "component-manager_replicas_max", "value": "6"}, {"name": "component-manager_replicas_min", "value": "2"}, {"name": "component-manager_cpu_request", "value": "0.5"}, {"name": "component-manager_mem_request", "value": "0.5Gi"}, {"name": "component-manager_cpu_limit", "value": "4.0"}, {"name": "component-manager_mem_limit", "value": "6.0Gi"}]}]}]}, {"app_name": "uedm-dg", "service_list": [{"service_name": "uedm-dg", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "dg-manager_replicas_max", "value": "6"}, {"name": "dg-manager_replicas_min", "value": "2"}, {"name": "dg-manager_cpu_request", "value": "0.5"}, {"name": "dg-manager_mem_request", "value": "0.5Gi"}, {"name": "dg-manager_cpu_limit", "value": "3.0"}, {"name": "dg-manager_mem_limit", "value": "4.0Gi"}]}]}]}, {"app_name": "uedm-grid", "service_list": [{"service_name": "uedm-grid", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "grid-manage_replicas_max", "value": "6"}, {"name": "grid-manage_replicas_min", "value": "2"}, {"name": "grid-manage_cpu_request", "value": "0.5"}, {"name": "grid-manage_mem_request", "value": "0.5Gi"}, {"name": "grid-manage_cpu_limit", "value": "2.0"}, {"name": "grid-manage_mem_limit", "value": "3.0Gi"}]}]}]}, {"app_name": "uedm-monitor", "service_list": [{"service_name": "uedm-monitor", "ms_list": [{"env_list": [{"name": "scene", "value": "site"}], "other_list": [{"name": "monitor-reconstruct_replicas_max", "value": "6"}, {"name": "monitor-reconstruct_replicas_min", "value": "2"}, {"name": "monitor-reconstruct_cpu_request", "value": "0.5"}, {"name": "monitor-reconstruct_mem_request", "value": "0.5Gi"}, {"name": "monitor-reconstruct_cpu_limit", "value": "6.0"}, {"name": "monitor-reconstruct_mem_limit", "value": "6.0Gi"}]}]}]}, {"app_name": "dex-ssm", "service_list": [{"service_name": "dex-ssm", "ms_list": [{"env_list": [{"name": "HIDE_MENU", "value": "otcp-dex-ssm-alarm-alarmManagement,otcp-dex-ssm-performance-microservicePerformance,otcp-dex-ssm-object-node,otcp-dex-ssm-object-databaseMonitoring,otcp-dex-ssm-log-logCollection"}]}]}]}, {"app_name": "doclite", "service_list": [{"service_name": "doclite", "ms_list": [{"env_list": [{"name": "enable_pkg_management", "value": "true"}]}]}]}, {"app_name": "fm", "service_list": [{"service_name": "fm", "ms_list": [{"env_list": [{"name": "supportAlamMaskingRule", "value": "false"}]}]}]}, {"service_list": [{"ms_list": [{"ms_name": "zae-ragknowledgebase-ms", "other_list": [{"name": "zae-ragknowledgebase_zae-ragknowledgebase-ms_replica_init", "value": "1"}, {"name": "zae-ragknowledgebase_zae-ragknowledgebase-ms_replica_min", "value": "1"}, {"name": "zae-ragknowledgebase_zae-ragknowledgebase-ms_replica_max", "value": "1"}, {"name": "zae-ragknowledgebase_zae-ragknowledgebase-ms_cpu_limit", "value": "2"}, {"name": "zae-ragknowledgebase_zae-ragknowledgebase-ms_mem_limit", "value": "8Gi"}, {"name": "zae-ragknowledgebase_zae-ragknowledgebase-ms_cpu_request", "value": "0.1"}, {"name": "zae-ragknowledgebase_zae-ragknowledgebase-ms_mem_request", "value": "0.1Gi"}]}], "service_name": "zae-ragknowledgebase"}, {"ms_list": [{"ms_name": "zae-ragpluginmanagement-ms", "other_list": [{"name": "zae-ragpluginmanagement_zae-ragpluginmanagement-ms_replica_init", "value": "1"}, {"name": "zae-ragpluginmanagement_zae-ragpluginmanagement-ms_replica_min", "value": "1"}, {"name": "zae-ragpluginmanagement_zae-ragpluginmanagement-ms_replica_max", "value": "1"}, {"name": "zae-ragpluginmanagement_zae-ragpluginmanagement-ms_limit_cpu", "value": "2"}, {"name": "zae-ragpluginmanagement_zae-ragpluginmanagement-ms_limit_memory", "value": "4Gi"}, {"name": "zae-ragpluginmanagement_zae-ragpluginmanagement-ms_request_cpu", "value": "0.4"}, {"name": "zae-ragpluginmanagement_zae-ragpluginmanagement-ms_request_memory", "value": "4Gi"}]}], "service_name": "zae-ragpluginmanagement"}, {"ms_list": [{"ms_name": "zae-ragpluginmanagementui", "other_list": [{"name": "zae_zae-ragpluginmanagementui_replica_init", "value": "1"}, {"name": "zae_zae-ragpluginmanagementui_replica_min", "value": "1"}, {"name": "zae_zae-ragpluginmanagementui_replica_max", "value": "1"}, {"name": "zae_zae-ragpluginmanagementui_cpu_limit", "value": "2"}, {"name": "zae_zae-ragpluginmanagementui_mem_limit", "value": "2Gi"}, {"name": "zae_zae-ragpluginmanagementui_cpu_request", "value": "1"}, {"name": "zae_zae-ragpluginmanagementui_mem_request", "value": "1Gi"}]}], "service_name": "zae-ragpluginmanagementui"}, {"ms_list": [{"ms_name": "zae-ragcomponentservice-ms", "other_list": [{"name": "zae-ragcomponentservice_zae-ragcomponentservice-ms_replica_init", "value": "1"}, {"name": "zae-ragcomponentservice_zae-ragcomponentservice-ms_replica_min", "value": "1"}, {"name": "zae-ragcomponentservice_zae-ragcomponentservice-ms_replica_max", "value": "1"}, {"name": "zae-ragcomponentservice_zae-ragcomponentservice-ms_cpu_limit", "value": "2"}, {"name": "zae-ragcomponentservice_zae-ragcomponentservice-ms_mem_limit", "value": "4Gi"}, {"name": "zae-ragcomponentservice_zae-ragcomponentservice-ms_cpu_request", "value": "0.4"}, {"name": "zae-ragcomponentservice_zae-ragcomponentservice-ms_mem_request", "value": "4Gi"}]}], "service_name": "zae-ragcomponentservice"}, {"ms_list": [{"ms_name": "zae-ragmediaserver-ms", "other_list": [{"name": "zae-ragmediaserver_zae-ragmediaserver-ms_replica_init", "value": "1"}, {"name": "zae-ragmediaserver_zae-ragmediaserver-ms_replica_min", "value": "1"}, {"name": "zae-ragmediaserver_zae-ragmediaserver-ms_replica_max", "value": "1"}, {"name": "zae-ragmediaserver_zae-ragmediaserver-ms_cpu_limit", "value": "1"}, {"name": "zae-ragmediaserver_zae-ragmediaserver-ms_mem_limit", "value": "2Gi"}, {"name": "zae-ragmediaserver_zae-ragmediaserver-ms_cpu_request", "value": "0.2"}, {"name": "zae-ragmediaserver_zae-ragmediaserver-ms_mem_request", "value": "2Gi"}]}], "service_name": "zae-ragmediaserver"}], "app_name": "ZAE-RAG"}, {"service_list": [{"ms_list": [{"ms_name": "zae-ragevaluation-ms", "other_list": [{"name": "zae-ragevaluation_zae-ragevaluation-ms_cpu_request", "value": "0.1"}, {"name": "zae-ragevaluation_zae-ragevaluation-ms_mem_request", "value": "0.5Gi"}, {"name": "zae-ragevaluation_zae-ragevaluation-ms_cpu_limit", "value": "4"}, {"name": "zae-ragevaluation_zae-ragevaluation-ms_mem_limit", "value": "4Gi"}, {"name": "zae-ragevaluation_zae-ragevaluation-ms_replica_max", "value": "1"}, {"name": "zae-ragevaluation_zae-ragevaluation-ms_replica_init", "value": "1"}]}], "service_name": "zae-ragevaluation"}], "app_name": "ZAE-RAG-Evaluation"}, {"service_list": [{"ms_list": [{"ms_name": "zae-bs-modeladapter-ms", "other_list": [{"name": "zae-bs-modeladapter_zae-bs-modeladapter-ms_cpu_request", "value": "1.0"}, {"name": "zae-bs-modeladapter_zae-bs-modeladapter-ms_mem_request", "value": "1.0Gi"}, {"name": "zae-bs-modeladapter_zae-bs-modeladapter-ms_cpu_limit", "value": "1"}, {"name": "zae-bs-modeladapter_zae-bs-modeladapter-ms_mem_limit", "value": "1.0Gi"}, {"name": "zae-bs-modeladapter_zae-bs-modeladapter-ms_replica_max", "value": "1"}, {"name": "zae-bs-modeladapter_zae-bs-modeladapter-ms_replica_min", "value": "1"}, {"name": "zae-bs-modeladapter_zae-bs-modeladapter-ms_replica_init", "value": "1"}]}], "service_name": "zae-bs-modeladapter"}], "app_name": "ZAE-BS-ModelAdapter"}, {"service_list": [{"ms_list": [{"ms_name": "nae-commoncomponent-ms", "other_list": [{"name": "nae-commoncomponent_nae-commoncomponent-ms_cpu_request", "value": "1.0"}, {"name": "nae-commoncomponent_nae-commoncomponent-ms_mem_request", "value": "1.0Gi"}, {"name": "nae-commoncomponent_nae-commoncomponent-ms_cpu_limit", "value": "1"}, {"name": "nae-commoncomponent_nae-commoncomponent-ms_mem_limit", "value": "1.0Gi"}, {"name": "nae-commoncomponent_nae-commoncomponent-ms_replica_max", "value": "1"}, {"name": "nae-commoncomponent_nae-commoncomponent-ms_replica_min", "value": "1"}, {"name": "nae-commoncomponent_nae-commoncomponent-ms_replica_init", "value": "1"}]}], "service_name": "nae-commoncomponent"}], "app_name": "NAE-CommonComponent"}, {"service_list": [{"ms_list": [{"ms_name": "zae-bs-promoptadapter-ms", "other_list": [{"name": "zae-bs-promptadapter_zae-bs-promptadapter-ms_cpu_request", "value": "1.0"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ms_mem_request", "value": "1.0Gi"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ms_cpu_limit", "value": "4"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ms_mem_limit", "value": "4.0Gi"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ms_replica_max", "value": "4"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ms_replica_min", "value": "1"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ms_replica_init", "value": "1"}]}, {"ms_name": "zae-bs-promoptadapter-ui", "other_list": [{"name": "zae-bs-promptadapter_zae-bs-promptadapter-ui_cpu_request", "value": "0.1"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ui_mem_request", "value": "128Mi"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ui_cpu_limit", "value": "1"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ui_mem_limit", "value": "1Gi"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ui_replica_max", "value": "1"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ui_replica_min", "value": "1"}, {"name": "zae-bs-promptadapter_zae-bs-promptadapter-ui_replica_init", "value": "1"}]}], "service_name": "zae-bs-promptadapter"}], "app_name": "ZAE-BS-PromptAdapter"}, {"service_list": [{"ms_list": [{"ms_name": "zae-apimappingservice-ms", "other_list": [{"name": "zae-apimappingservice_zae-apimappingservice-ms_cpu_limit", "value": "2"}, {"name": "zae-apimappingservice_zae-apimappingservice-ms_cpu_request", "value": "0.4"}, {"name": "zae-apimappingservice_zae-apimappingservice-ms_mem_limit", "value": "4Gi"}, {"name": "zae-apimappingservice_zae-apimappingservice-ms_mem_request", "value": "4Gi"}, {"name": "zae-apimappingservice_zae-apimappingservice-ms_replica_max", "value": "1"}, {"name": "zae-apimappingservice_zae-apimappingservice-ms_replica_min", "value": "1"}, {"name": "zae-apimappingservice_zae-apimappingservice-ms_replica_init", "value": "1"}]}], "service_name": "zae-apimappingservice"}], "app_name": "ZAE-API"}, {"service_list": [{"ms_list": [{"ms_name": "zae-apirefineservice-ms", "other_list": [{"name": "zae-apirefineservice_zae-apirefineservice-ms_cpu_limit", "value": "1.4"}, {"name": "zae-apirefineservice_zae-apirefineservice-ms_cpu_request", "value": "0.28"}, {"name": "zae-apirefineservice_zae-apirefineservice-ms_mem_limit", "value": "1.75Gi"}, {"name": "zae-apirefineservice_zae-apirefineservice-ms_mem_request", "value": "1.75Gi"}, {"name": "zae-apirefineservice_zae-apirefineservice-ms_replica_max", "value": "1"}, {"name": "zae-apirefineservice_zae-apirefineservice-ms_replica_min", "value": "1"}, {"name": "zae-apirefineservice_zae-apirefineservice-ms_replica_init", "value": "1"}]}], "service_name": "zae-apirefineservice"}], "app_name": "ZAE-APIREFINE"}, {"service_list": [{"ms_list": [{"ms_name": "workbridge-studio-iui-ms", "other_list": [{"name": "workbridge-studio-iui_workbridge-studio-iui-ms_cpu_limit", "value": "0.1"}, {"name": "workbridge-studio-iui_workbridge-studio-iui-ms_cpu_request", "value": "0.1"}, {"name": "workbridge-studio-iui_workbridge-studio-iui-ms_mem_limit", "value": "0.05Gi"}, {"name": "workbridge-studio-iui_workbridge-studio-iui-ms_mem_request", "value": "0.02Gi"}, {"name": "workbridge-studio-iui_workbridge-studio-iui-ms_replica_max", "value": "1"}, {"name": "workbridge-studio-iui_workbridge-studio-iui-ms_replica_min", "value": "1"}, {"name": "workbridge-studio-iui_workbridge-studio-iui-ms_replica_init", "value": "1"}]}], "service_name": "workbridge-studio-iui"}], "app_name": "zae-APIfrontendTool"}, {"service_list": [{"ms_list": [{"ms_name": "nae-assetcenterservice-ms", "other_list": [{"name": "nae-assetcenterservice_nae-assetcenterservice-ms_replica_init", "value": "1"}, {"name": "nae-assetcenterservice_nae-assetcenterservice-ms_replica_min", "value": "1"}, {"name": "nae-assetcenterservice_nae-assetcenterservice-ms_replica_max", "value": "1"}, {"name": "nae-assetcenterservice_nae-assetcenterservice-ms_cpu_limit", "value": "2"}, {"name": "nae-assetcenterservice_nae-assetcenterservice-ms_mem_limit", "value": "4Gi"}, {"name": "nae-assetcenterservice_nae-assetcenterservice-ms_cpu_request", "value": "0.1"}, {"name": "nae-assetcenterservice_nae-assetcenterservice-ms_mem_request", "value": "0.1Gi"}]}], "service_name": "nae-assetcenterservice"}, {"ms_list": [{"ms_name": "nae-assetcenterservice-iui-ms", "other_list": [{"name": "nae-assetcenterservice-iui_nae-assetcenterservice-iui-ms_replica_init", "value": "1"}, {"name": "nae-assetcenterservice-iui_nae-assetcenterservice-iui-ms_replica_min", "value": "1"}, {"name": "nae-assetcenterservice-iui_nae-assetcenterservice-iui-ms_replica_max", "value": "1"}, {"name": "nae-assetcenterservice-iui_nae-assetcenterservice-iui-ms_cpu_limit", "value": "0.1"}, {"name": "nae-assetcenterservice-iui_nae-assetcenterservice-iui-ms_mem_limit", "value": "0.05Gi"}, {"name": "nae-assetcenterservice-iui_nae-assetcenterservice-iui-ms_cpu_request", "value": "0.1"}, {"name": "nae-assetcenterservice-iui_nae-assetcenterservice-iui-ms_mem_request", "value": "0.02Gi"}]}], "service_name": "nae-assetcenterservice-iui"}], "app_name": "NAE-ASSETCENTER"}, {"service_list": [{"ms_list": [{"ms_name": "ifactory-agent-run-ms", "other_list": [{"name": "ifactory-agent-run_requests_cpu", "value": "1"}, {"name": "ifactory-agent-run_requests_memory", "value": "1.0Gi"}, {"name": "ifactory-agent-run_limit_cpu", "value": "4"}, {"name": "ifactory-agent-run_limit_memory", "value": "4.0Gi"}]}], "service_name": "ifactory-agent-run"}], "app_name": "NAE-IFACTORY-AG<PERSON>TRUN"}, {"service_list": [{"ms_list": [{"ms_name": "ifactory-agent-dev-ms", "other_list": [{"name": "ifactory-agent-dev_requests_cpu", "value": "1"}, {"name": "ifactory-agent-dev_requests_memory", "value": "1.0Gi"}, {"name": "ifactory-agent-dev_limit_cpu", "value": "2"}, {"name": "ifactory-agent-dev_limit_memory", "value": "2.0Gi"}]}], "service_name": "ifactory-agent-dev"}, {"ms_list": [{"ms_name": "ifactory-portal-ui-ms", "other_list": [{"name": "ifactory-portal-ui_requests_cpu", "value": "0.1"}, {"name": "ifactory-portal-ui_requests_memory", "value": "0.1Gi"}, {"name": "ifactory-portal-ui_limit_cpu", "value": "1"}, {"name": "ifactory-portal-ui_limit_memory", "value": "1.0Gi"}]}], "service_name": "ifactory-portal-ui"}], "app_name": "NAE-IFACTORY-AGENTDEV"}, {"app_name": "ZAE-AGENT", "service_list": [{"service_name": "zagents-manager-service", "ms_list": [{"ms_name": "zagents-manager-service", "other_list": [{"name": "zagents-manager-service_requests_cpu", "value": "0.2"}, {"name": "zagents-manager-service_requests_memory", "value": "0.2Gi"}, {"name": "zagents-manager-service_limit_cpu", "value": "1"}, {"name": "zagents-manager-service_limit_memory", "value": "1Gi"}, {"name": "zagents-manager-service_replicas", "value": "1"}, {"name": "zagents-manager-service_replicas_min", "value": "1"}, {"name": "zagents-manager-service_replicas_max", "value": "1"}]}]}]}, {"app_name": "NAE-IFACTORY-NAEE", "service_list": [{"service_name": "evaluation-center-service", "ms_list": [{"ms_name": "evaluation-center-service", "other_list": [{"name": "evaluation-center-service_requests_cpu", "value": "0.2"}, {"name": "evaluation-center-service_requests_memory", "value": "0.3Gi"}, {"name": "evaluation-center-service_limit_cpu", "value": "1"}, {"name": "evaluation-center-service_limit_memory", "value": "1Gi"}, {"name": "evaluation-center-service_replicas", "value": "1"}, {"name": "evaluation-center-service_replicas_min", "value": "1"}, {"name": "evaluation-center-service_replicas_max", "value": "1"}]}]}, {"service_name": "evaluation-center-ui", "ms_list": [{"ms_name": "evaluation-center-ui", "other_list": [{"name": "evaluation-center-ui_requests_cpu", "value": "0.2"}, {"name": "evaluation-center-ui_requests_memory", "value": "0.3Gi"}, {"name": "evaluation-center-ui_limit_cpu", "value": "0.5"}, {"name": "evaluation-center-ui_limit_memory", "value": "0.5Gi"}, {"name": "evaluation-center-ui_replicas", "value": "1"}, {"name": "evaluation-center-ui_replicas_min", "value": "1"}, {"name": "evaluation-center-ui_replicas_max", "value": "1"}]}]}, {"service_name": "evaluation-center-run-service", "ms_list": [{"ms_name": "evaluation-center-run-service", "other_list": [{"name": "evaluation-center-run-service_requests_cpu", "value": "0.3"}, {"name": "evaluation-center-run-service_requests_memory", "value": "0.5Gi"}, {"name": "evaluation-center-run-service_limit_cpu", "value": "1"}, {"name": "evaluation-center-run-service_limit_memory", "value": "2Gi"}, {"name": "evaluation-center-run-service_replicas", "value": "1"}, {"name": "evaluation-center-run-service_replicas_min", "value": "1"}, {"name": "evaluation-center-run-service_replicas_max", "value": "1"}]}]}]}, {"service_list": [{"ms_list": [{"ms_name": "zae-chatbot-server", "other_list": [{"name": "zae-chatbot-service_zae-chatbot-server_cpu_request", "value": "0.5"}, {"name": "zae-chatbot-service_zae-chatbot-server_mem_request", "value": "1.0Gi"}, {"name": "zae-chatbot-service_zae-chatbot-server_cpu_limit", "value": "4.0"}, {"name": "zae-chatbot-service_zae-chatbot-server_mem_limit", "value": "4.0Gi"}, {"name": "zae-chatbot-service_zae-chatbot-server_replica_init", "value": "1"}, {"name": "zae-chatbot-service_zae-chatbot-server_replica_min", "value": "1"}, {"name": "zae-chatbot-service_zae-chatbot-server_replica_max", "value": "1"}, {"name": "zae-chatbot-service_zae-chatbot-lui_cpu_request", "value": "0.01"}, {"name": "zae-chatbot-service_zae-chatbot-lui_mem_request", "value": "0.02Gi"}, {"name": "zae-chatbot-service_zae-chatbot-lui_cpu_limit", "value": "0.1"}, {"name": "zae-chatbot-service_zae-chatbot-lui_mem_limit", "value": "0.05Gi"}, {"name": "zae-chatbot-service_zae-chatbot-lui_replica_init", "value": "1"}, {"name": "zae-chatbot-service_zae-chatbot-lui_replica_min", "value": "1"}, {"name": "zae-chatbot-service_zae-chatbot-lui_replica_max", "value": "1"}]}], "service_name": "zae-chatbot-service"}], "app_name": "zae-chatbot-service"}, {"service_list": [{"ms_list": [{"other_list": [{"name": "aab-agentschedule_aab-agentschedule-ms_request_cpu", "value": "1"}, {"name": "aab-agentschedule_aab-agentschedule-ms_request_memory", "value": "2Gi"}, {"name": "aab-agentschedule_aab-agentschedule-ms_limit_cpu", "value": "5"}, {"name": "aab-agentschedule_aab-agentschedule-ms_limit_memory", "value": "5Gi"}, {"name": "aab-agentschedule_aab-agentschedule-ms_replica_max", "value": "1"}, {"name": "aab-agentschedule_aab-agentschedule-ms_replica_min", "value": "1"}, {"name": "aab-agentschedule_aab-agentschedule-ms_replica_init", "value": "1"}]}], "service_name": "aab-agentschedule"}], "app_name": "aab-agentschedule"}, {"service_list": [{"ms_list": [{"other_list": [{"name": "aab-pytools_aab-pytools-ms_request_cpu", "value": "4"}, {"name": "aab-pytools_aab-pytools-ms_request_memory", "value": "4.0Gi"}, {"name": "aab-pytools_aab-pytools-ms_limit_cpu", "value": "8"}, {"name": "aab-pytools_aab-pytools-ms_limit_memory", "value": "8.0Gi"}, {"name": "aab-pytools_aab-pytools-ms_replica_max", "value": "2"}, {"name": "aab-pytools_aab-pytools-ms_replica_min", "value": "2"}, {"name": "aab-pytools_aab-pytools-ms_replica_init", "value": "2"}]}], "service_name": "aab-pytools"}], "app_name": "aab-pytools"}, {"service_list": [{"ms_list": [{"other_list": [{"name": "aab-portal_aab-portal_cpu_request", "value": "0.5"}, {"name": "aab-portal_aab-portal_mem_request", "value": "0.3Gi"}, {"name": "aab-portal_aab-portal_cpu_limit", "value": "1"}, {"name": "aab-portal_aab-portal_mem_limit", "value": "0.5Gi"}, {"name": "aab-portal_aab-portal_replica_max", "value": "1"}, {"name": "aab-portal_aab-portal_replica_min", "value": "1"}, {"name": "aab-portal_aab-portal_replica_init", "value": "1"}]}], "service_name": "aab-portal"}], "app_name": "aab-portal"}, {"service_list": [{"ms_list": [{"other_list": [{"name": "aab-workflow_aab-workflow-ms_request_cpu", "value": "1"}, {"name": "aab-workflow_aab-workflow-ms_request_memory", "value": "1.0Gi"}, {"name": "aab-workflow_aab-workflow-ms_limit_cpu", "value": "2"}, {"name": "aab-workflow_aab-workflow-ms_limit_memory", "value": "4.0Gi"}, {"name": "aab-workflow_aab-workflow-ms_replica_max", "value": "2"}, {"name": "aab-workflow_aab-workflow-ms-replica_min", "value": "2"}, {"name": "aab-workflow_aab-workflow-ms_replica_init", "value": "2"}]}], "service_name": "aab-workflow"}], "app_name": "aab-workflow"}, {"service_list": [{"ms_list": [{"other_list": [{"name": "aab-agent_aab-agent-ms_request_cpu", "value": "1"}, {"name": "aab-agent_aab-agent-ms_request_memory", "value": "1.0Gi"}, {"name": "aab-agent_aab-agent-ms_limit_cpu", "value": "2"}, {"name": "aab-agent_aab-agent-ms_limit_memory", "value": "4.0Gi"}, {"name": "aab-agent_aab-agent-ms_replica_max", "value": "1"}, {"name": "aab-agent_aab-agent-ms_replica_min", "value": "1"}, {"name": "aab-agent_aab-agent-ms_replica_init", "value": "1"}]}], "service_name": "aab-agent"}], "app_name": "aab-agent"}, {"service_list": [{"ms_list": [{"other_list": [{"name": "aab-kbase_aab-kbase-ms_request_cpu", "value": "1"}, {"name": "aab-kbase_aab-kbase-ms_request_memory", "value": "2.0Gi"}, {"name": "aab-kbase_aab-kbase-ms_limit_cpu", "value": "4"}, {"name": "aab-kbase_aab-kbase-ms_limit_memory", "value": "8.0Gi"}, {"name": "aab-kbase_aab-kbase-ms_replica_max", "value": "2"}, {"name": "aab-kbase_aab-kbase-ms_replica_min", "value": "2"}, {"name": "aab-kbase_aab-kbase-ms_replica_init", "value": "2"}]}], "service_name": "aab-kbase"}], "app_name": "aab-kbase"}], "networks": [{"net_name": "net_api", "owner": "admin", "ip_groups": [{"num": "1", "name": "gbase-node"}, {"num": "1", "name": "gbase-cluster"}, {"name": "uedm-south-agent", "num": "1"}]}], "plugin_paras": [{"name": "executevalue", "value": "true"}]}