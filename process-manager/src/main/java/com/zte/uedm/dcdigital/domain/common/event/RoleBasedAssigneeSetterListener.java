package com.zte.uedm.dcdigital.domain.common.event;

import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.domain.common.constant.ProcessConstants;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProcessStatusCode;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RoleBasedAssigneeSetterListener implements TaskListener {


    @Autowired
    @Qualifier("systemServiceImpl")
    private SystemService systemService;


    private static RoleBasedAssigneeSetterListener roleListener;

    @PostConstruct
    public void init(){
        roleListener = this;
        roleListener.systemService = this.systemService;
    }

    /* Started by AICoder, pid:x1b13y30550133414e930a43604fae24e9d8097f */

    @Override
    public void notify(DelegateTask delegateTask) {
        // 获取当前任务ID
        String taskId = delegateTask.getId();
        // 获取自定义产品类别
        String productCategory = (String) delegateTask.getVariable(ProcessConstants.PRODUCT_CATEGORY);
        if (productCategory == null) {
            log.error("productCategory is null, taskId:{}", taskId);
            throw new BusinessException(ProcessStatusCode.PRODUCT_CATEGORY_NOT_FOUND);
        }
        // 获取定义的节点key
        String taskKey = delegateTask.getTaskDefinitionKey();
        log.info("taskKey, productCategory: {}, {}", taskKey, productCategory);
        // 根据节点key和产品类别获取角色
        List<String> userIds = queryAssigneesForRoleAndCategory(taskKey, productCategory);
        if (userIds != null && !userIds.isEmpty()) {
            // 设置任务候选人
            delegateTask.addCandidateUsers(userIds);
            log.info("Added {} candidates to task {}", userIds.size(), taskId);
        } else {
            log.error("No candidates found for task {}, productCategory:{}", taskId, productCategory);
            throw new BusinessException(ProcessStatusCode.NO_CANDIDATES_FOUND);
        }
    }
    /* Ended by AICoder, pid:x1b13y30550133414e930a43604fae24e9d8097f */

    /* Started by AICoder, pid:j1b03ad6f950f3a1446c0a18a0ef9a1920993522 */
    /**
     * 根据任务键和产品类别查询分配的用户列表。
     *
     * @param taskKey        任务键，用于标识特定的任务。
     * @param productCategory 产品类别，用于进一步筛选用户。
     * @return 返回符合条件的用户ID列表。如果没有找到任何用户，返回一个空列表。
     */
    private List<String> queryAssigneesForRoleAndCategory(String taskKey, String productCategory) {
        log.info("taskKey, productCategory: {}, {}", taskKey, productCategory);
        boolean present = RoleCodeEnum.isEnumValuePresent(taskKey);
        if (present) {
            List<UserVo> userInfo = roleListener.systemService.getUserByRoleCodeAndResourceId(taskKey, productCategory);
            log.info("userInfo size: {}", userInfo.size());
            return Optional.ofNullable(userInfo)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(UserVo::getId)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    /* Ended by AICoder, pid:j1b03ad6f950f3a1446c0a18a0ef9a1920993522 */
}
