package com.zte.uedm.dcdigital.domain.common.constant;

import com.google.common.collect.ImmutableMap;

public class MktTaskNumMapConstants {
    // 使用不可变Map确保线程安全，键值对更清晰
    public static final ImmutableMap<String, String> ACTIVITY_TO_TASK_NUM_MAP =
            ImmutableMap.<String, String>builder()
                    .put(MktTaskConstants.MATERIAL_SELECTION_ID, MktTaskConstants.MATERIEL_TASK_NUM)
                    .put(MktTaskConstants.BID_CLARIFICATION_ID, MktTaskConstants.TENDER_TASK_NUM)
                    .put(MktTaskConstants.DOCUMENTATION_ID, MktTaskConstants.DOCUMENT_TASK_NUM)
                    .build();

    // 映射任务ID到任务后缀
    public static final ImmutableMap<String, String> TASK_SUFFIX_MAP =
            ImmutableMap.<String, String>builder()
                    .put(MktTaskConstants.MATERIAL_SELECTION_ID, "物料选型")
                    .put(MktTaskConstants.BID_CLARIFICATION_ID, "标书澄清")
                    .put(MktTaskConstants.DOCUMENTATION_ID, "文档编写")
                    .build();
    //任务验证通过映射对应的子任务静态任务id
    public static final ImmutableMap<String, String> TASK_AUTH_MAP =
            ImmutableMap.<String, String>builder()
                    .put(MktTaskConstants.MATERIAL_SELECTION_AUTH_ID, MktTaskConstants.MATERIAL_SELECTION_ID)
                    .put(MktTaskConstants.BID_CLARIFICATION_AUTH_ID, MktTaskConstants.BID_CLARIFICATION_ID)
                    .put(MktTaskConstants.DOCUMENTATION_AUTH_ID, MktTaskConstants.DOCUMENTATION_ID)
                    .build();
    // 可选：添加默认值保护
    public static final String DEFAULT_TASK_NUM = "defaultTaskNum";
}