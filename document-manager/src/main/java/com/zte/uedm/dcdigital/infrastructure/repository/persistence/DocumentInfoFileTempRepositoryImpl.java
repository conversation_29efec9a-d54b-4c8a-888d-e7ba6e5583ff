/* Started by AICoder, pid:h319d7a0b9k878114b6909548061a29ed7f37933 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentInfoFileTempRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocumentInfoFileTempMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentInfoFileTempPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 文档信息临时文件仓库实现类。
 */
@Slf4j
@Repository
public class DocumentInfoFileTempRepositoryImpl extends ServiceImpl<DocumentInfoFileTempMapper, DocumentInfoFileTempPo> implements DocumentInfoFileTempRepository {
    @Override
    public void deleteByDocumentId(String id) {
        LambdaQueryWrapper<DocumentInfoFileTempPo> wrapper = Wrappers.<DocumentInfoFileTempPo>lambdaQuery().eq(DocumentInfoFileTempPo::getDocumentInfoId, id);
        this.remove(wrapper);
    }


    @Override
    public void batchAddFileIds(String id, List<String> fileIds,String userId) {
        if (CollectionUtils.isNotEmpty(fileIds)) {
            List<DocumentInfoFileTempPo> list = new ArrayList<>();
            for (String fileId : fileIds) {
                DocumentInfoFileTempPo po = new DocumentInfoFileTempPo();
                po.setDocumentInfoId(id);
                po.setFileId(fileId);
                po.setCreateBy(userId);
                list.add(po);
            }
            this.saveBatch(list);
        }
    }


    @Override
    public List<DocumentInfoFileTempPo> queryByDocumentIds(Set<String> documentIds, String userId) {
        if (CollectionUtils.isEmpty(documentIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DocumentInfoFileTempPo> wrapper = Wrappers.<DocumentInfoFileTempPo>lambdaQuery()
                .eq(DocumentInfoFileTempPo::getCreateBy, userId)
                .in(DocumentInfoFileTempPo::getDocumentInfoId, documentIds);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public void deleteByDocumentIdsAndUser(List<String> tempIds, String userId) {
        if (CollectionUtils.isEmpty(tempIds)) {
            return;
        }
        LambdaQueryWrapper<DocumentInfoFileTempPo> wrapper = Wrappers.<DocumentInfoFileTempPo>lambdaQuery()
                .eq(DocumentInfoFileTempPo::getCreateBy, userId)
                .in(DocumentInfoFileTempPo::getDocumentInfoId, tempIds);
        this.remove(wrapper);
    }
}
/* Ended by AICoder, pid:h319d7a0b9k878114b6909548061a29ed7f37933 */