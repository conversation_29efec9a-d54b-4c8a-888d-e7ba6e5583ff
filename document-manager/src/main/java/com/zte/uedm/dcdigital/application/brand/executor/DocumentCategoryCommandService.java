package com.zte.uedm.dcdigital.application.brand.executor;

import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCategoryAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocumentCategoryEditDto;

public interface DocumentCategoryCommandService {

    void addDocumentCategory(DocumentCategoryAddDto addDTO);

    void updateDocumentCategory(DocumentCategoryEditDto editDTO);

    void deleteDocumentCategory(String id);
}
