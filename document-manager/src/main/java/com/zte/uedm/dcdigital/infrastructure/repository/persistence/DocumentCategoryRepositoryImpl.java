package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DocumentCategoryRepository;
import com.zte.uedm.dcdigital.domain.common.valueobj.DocumentCategoryObj;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.DocumentCategoryConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DocumentCategoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DocumentCategoryPo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocumentCategoryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class DocumentCategoryRepositoryImpl extends ServiceImpl<DocumentCategoryMapper, DocumentCategoryPo>
        implements DocumentCategoryRepository {

    @Autowired
    private DocumentCategoryMapper documentCategoryMapper;

    /* Started by AICoder, pid:oe0a9v7497i89a714db80adf4060b54b47801621 */

    @Override
    public List<DocumentCategoryVo> queryList(String name, Integer type) {
        QueryWrapper<DocumentCategoryPo> queryWrapper = new QueryWrapper<>();
        if (name != null && !name.trim().isEmpty()) {
            queryWrapper.like("name", name.toLowerCase());
            // 注意：MyBatis Plus 的 like 方法已经支持大小写不敏感查询，无需显式转换为小写
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        queryWrapper.orderByAsc("create_time");
        List<DocumentCategoryPo> list = documentCategoryMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return DocumentCategoryConverter.INSTANCE.toVoList(list);
    }

    @Override
    public int addDocumentCategory(DocumentCategoryObj entity) {
        DocumentCategoryPo po = DocumentCategoryConverter.INSTANCE.toPo(entity);
        return documentCategoryMapper.insert(po);
    }

    @Override
    public int updateDocumentCategory(DocumentCategoryObj entity) {
        DocumentCategoryPo po = DocumentCategoryConverter.INSTANCE.toPo(entity);
        return documentCategoryMapper.updateById(po);
    }

    @Override
    public int deleteDocumentCategory(String id) {
        return documentCategoryMapper.deleteById(id);
    }

    @Override
    public DocumentCategoryObj queryDocumentCategory(String id) {
        DocumentCategoryPo po = documentCategoryMapper.selectById(id);
        return po != null ? DocumentCategoryConverter.INSTANCE.toEntity(po) : null;
    }

    @Override
    public int selectUniqueByIdAndName(String id, String name) {
        return documentCategoryMapper.selectUniqueByIdAndName(id, name);
    }

    @Override
    public int selectCitedCountById(String id) {
        return documentCategoryMapper.selectCitedCountById(id);
    }

    /* Started by AICoder, pid:kde4992c83gac0a1429e09a3b0a81817aab2172a */
    @Override
    public List<DocumentCategoryVo> selectByName(String documentCategoryName) {
        LambdaQueryWrapper<DocumentCategoryPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DocumentCategoryPo::getName, documentCategoryName);
        List<DocumentCategoryPo> documentCategoryPos = documentCategoryMapper.selectList(queryWrapper);
        return DocumentCategoryConverter.INSTANCE.toVoList(documentCategoryPos);
    }
    /* Ended by AICoder, pid:kde4992c83gac0a1429e09a3b0a81817aab2172a */
    /* Ended by AICoder, pid:oe0a9v7497i89a714db80adf4060b54b47801621 */
}
