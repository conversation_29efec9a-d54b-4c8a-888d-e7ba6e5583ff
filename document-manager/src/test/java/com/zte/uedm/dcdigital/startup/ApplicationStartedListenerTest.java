/* Started by AICoder, pid:w09cfk2cf2w9bfd14b75088540d2503227c755be */
package com.zte.uedm.dcdigital.startup;

//import com.zte.uedm.dcdigital.application.brand.scheduler.DeleteTempFileScheduleJob;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.context.event.ApplicationStartedEvent;

import static org.mockito.Mockito.*;

public class ApplicationStartedListenerTest {

    @InjectMocks
    private ApplicationStartedListener applicationStartedListener;

//    @Mock
//    private DeleteTempFileScheduleJob deleteTempFileScheduleJob;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testOnApplicationEvent() {
        // Arrange
        ApplicationStartedEvent event = mock(ApplicationStartedEvent.class);

        // Act
        applicationStartedListener.onApplicationEvent(event);

        // Assert
//        verify(deleteTempFileScheduleJob, times(1)).execute();
    }
}

/* Ended by AICoder, pid:w09cfk2cf2w9bfd14b75088540d2503227c755be */