<?xml version="1.0" encoding="UTF-8" ?>
<!-- Started by AICoder, pid:b220a3bd13n9d97147080b01902ece230df0ef76 -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectAreaMapper">

    <select id="queryByCondition"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo">
        <!-- 使用动态SQL构建查询条件 -->
        SELECT *
        FROM project_area
        WHERE 1 = 1
        <if test="areaName != null and areaName != ''">
            AND area_name like CONCAT('%', #{areaName}, '%')
        </if>
        <if test="parentId != null and parentId != ''">
            AND parent_id = #{parentId}
        </if>
        ORDER BY create_time ASC
    </select>
    <select id="queryByParentId"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo">
        SELECT *
        FROM project_area
        WHERE parent_id = #{parentId}
    </select>
    <select id="selectAreaListByIdsAndName"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo">
        with recursive area_tree_up as (
        select id,area_name,parent_id,area_level,path_name
        from project_area
            <where>
                <if test="areaIds != null and areaIds.size() > 0">
                    id in
                    <foreach collection="areaIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="areaName != null and areaName != ''">
                    and area_name like concat('%',#{areaName},'%')
                </if>
            </where>
            union all
            -- 向上查询
            select pa.id,pa.area_name,pa.parent_id,pa.area_level,pa.path_name
            from project_area pa
            inner join area_tree_up at on pa.id = at.parent_id
        ),
        area_tree_down as (
            select id,area_name,parent_id,area_level,path_name
            from project_area
            <where>
                <if test="areaIds != null and areaIds.size() > 0">
                    id in
                    <foreach collection="areaIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="areaName != null and areaName != ''">
                    and area_name like concat('%',#{areaName},'%')
                </if>
            </where>
            union all
            -- 向下查询
            select pa.id,pa.area_name,pa.parent_id,pa.area_level,pa.path_name
            from project_area pa
            inner join area_tree_down atd on pa.parent_id = atd.id
        ) select distinct * from area_tree_up union select distinct * from area_tree_down;
    </select>
    <select id="selectAllAreaIds" resultType="java.lang.String">
        with recursive area_tree as (
            select id,parent_id
            from project_area
            where id = #{areaId}
            union all
            select pa.id,pa.parent_id
            from project_area pa
            inner join area_tree at on pa.parent_id = at.id
        ) select distinct id from area_tree;
    </select>
    <select id="selectAreaListByName"
            resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo">
        with recursive area_tree_up as (
        select id,area_name,parent_id,area_level,path_name
        from project_area
        <where>
            <if test="areaName != null and areaName != ''">
                area_name like concat('%',#{areaName},'%')
            </if>
        </where>
        union all
        -- 向上查询
        select pa.id,pa.area_name,pa.parent_id,pa.area_level,pa.path_name
        from project_area pa
        inner join area_tree_up at on pa.id = at.parent_id
        ),
        area_tree_down as (
        select id,area_name,parent_id,area_level,path_name
        from project_area
        <where>
            <if test="areaName != null and areaName != ''">
                 area_name like concat('%',#{areaName},'%')
            </if>
        </where>
        union all
        -- 向下查询
        select pa.id,pa.area_name,pa.parent_id,pa.area_level,pa.path_name
        from project_area pa
        inner join area_tree_down atd on pa.parent_id = atd.id
        ) select distinct * from area_tree_up union select distinct * from area_tree_down;
    </select>

    <!-- 查询所有地区信息 -->
    <select id="selectAllAreas" resultType="com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo">
        SELECT id, area_name, parent_id, path_name, path_id, area_level, create_time, update_time, create_by, update_by
        FROM project_area
        ORDER BY area_level ASC, area_name ASC
    </select>

</mapper>
        <!-- Ended by AICoder, pid:b220a3bd13n9d97147080b01902ece230df0ef76 -->