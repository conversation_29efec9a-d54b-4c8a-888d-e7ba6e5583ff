package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/* Started by AICoder, pid:6d539v7244k908f14b2109ceb041151defd28431 */
@Mapper
public interface ProjectAreaMapper extends BaseMapper<ProjectAreaPo> {

    /**
     * 根据区域名称和父ID查询项目区域信息。
     *
     * @param areaName 区域名称
     * @param parentId 父级ID
     * @return 区域信息列表
     */
    List<ProjectAreaPo> queryByCondition(@Param("areaName") String areaName, @Param("parentId") String parentId);

    List<ProjectAreaPo> selectAreaListByIdsAndName(@Param("areaIds") List<String> areaIds, @Param("areaName") String areaName);

    List<ProjectAreaPo> queryByParentId(@Param("parentId") String parentId);

    List<String> selectAllAreaIds(@Param("areaId") String areaId);

    List<ProjectAreaPo> selectAreaListByName(@Param("areaName") String areaName);

    /**
     * 查询所有地区信息，返回ID和名称的映射
     */
    List<ProjectAreaPo> selectAllAreas();
}
/* Ended by AICoder, pid:6d539v7244k908f14b2109ceb041151defd28431 */
