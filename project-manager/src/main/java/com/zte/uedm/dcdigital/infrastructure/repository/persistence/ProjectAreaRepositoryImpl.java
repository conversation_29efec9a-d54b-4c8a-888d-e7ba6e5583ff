/* Started by AICoder, pid:zb37egbb28v5f6a14578092c70c3883d10e4cd57 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ProjectAreaRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.ProjectAreaConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectAreaMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectAreaPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaQueryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProjectAreaRepositoryImpl implements ProjectAreaRepository {
    @Autowired
    ProjectAreaMapper projectAreaMapper;
    @Override
    public ProjectAreaObj selectById(String id) {
        ProjectAreaPo projectAreaPo = projectAreaMapper.selectById(id);
        return ProjectAreaConverter.INSTANCE.convertPo2Obj(projectAreaPo);
    }

    @Override
    public ProjectAreaObj selectByName(String name, Integer areaLevel) {
        QueryWrapper<ProjectAreaPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("area_name", name);
        queryWrapper.eq("area_level", areaLevel);
        ProjectAreaPo projectAreaPo = projectAreaMapper.selectOne(queryWrapper);
        return ProjectAreaConverter.INSTANCE.convertPo2Obj(projectAreaPo);
    }

    @Override
    public List<ProjectAreaObj> selectByParentId(String parentId) {
        List<ProjectAreaPo> pos = projectAreaMapper.queryByParentId(parentId);
        return ProjectAreaConverter.INSTANCE.convertPos2Objs(pos);
    }

    @Override
    public PageVO<ProjectAreaObj> queryByCondition(ProjectAreaQueryDto queryDto) {
        Page<ProjectAreaPo> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        List<ProjectAreaPo> projectAreaPos = projectAreaMapper.queryByCondition(queryDto.getAreaName(), queryDto.getParentId());
        List<ProjectAreaObj> projectAreaObjs = ProjectAreaConverter.INSTANCE.convertPos2Objs(projectAreaPos);
        PageVO<ProjectAreaObj> pageVO = new PageVO<>(page.getTotal(), projectAreaObjs);
        pageVO.setPages(page.getPages());
        PageHelper.clearPage();
        return pageVO;
    }

    @Override
    public void insert(ProjectAreaObj projectAreaObj) {
        ProjectAreaPo projectAreaPo = ProjectAreaConverter.INSTANCE.convertObj2Po(projectAreaObj);
        projectAreaMapper.insert(projectAreaPo);
    }

    @Override
    public void update(ProjectAreaObj projectAreaObj) {
        ProjectAreaPo projectAreaPo = ProjectAreaConverter.INSTANCE.convertObj2Po(projectAreaObj);
        projectAreaMapper.updateById(projectAreaPo);
    }

    @Override
    public void deleteById(String id) {
        projectAreaMapper.deleteById(id);
    }

    @Override
    public List<ProjectAreaObj> selectAreaListBy(List<String> areaIds, String areaName) {
        List<ProjectAreaPo> projectAreaPos = projectAreaMapper.selectAreaListByIdsAndName(areaIds, areaName);
        return ProjectAreaConverter.INSTANCE.convertPos2Objs(projectAreaPos);
    }

    @Override
    public List<String> selectAllAreaIds(String areaId) {
        return projectAreaMapper.selectAllAreaIds(areaId);
    }

    @Override
    public List<ProjectAreaObj> selectAreaListByName(String areaName) {
        List<ProjectAreaPo> projectAreaPos = projectAreaMapper.selectAreaListByName(areaName);
        return ProjectAreaConverter.INSTANCE.convertPos2Objs(projectAreaPos);
    }

    @Override
    public List<ProjectAreaObj> selectAllAreas() {
        List<ProjectAreaPo> projectAreaPos = projectAreaMapper.selectAllAreas();
        return ProjectAreaConverter.INSTANCE.convertPos2Objs(projectAreaPos);
    }
}
/* Ended by AICoder, pid:zb37egbb28v5f6a14578092c70c3883d10e4cd57 */