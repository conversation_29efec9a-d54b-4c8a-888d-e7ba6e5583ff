package com.zte.uedm.dcdigital.domain.aggregate.repository;

/* Started by AICoder, pid:t02cdrd1ceo70921448e0a1210d5ef8e14e892c2 */

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectEntity;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.CustomerVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.ProjectVo;

import java.util.List;

/**
 * 项目数据访问接口，定义了与项目相关的数据库操作方法。
 */
public interface ProjectRepository {

    /**
     * 根据客户名称查询客户列表。
     *
     * @param name 客户名称。
     * @return 包含匹配客户的列表。
     */
    List<CustomerVo> selectCustomers(String name);

    /**
     * 根据查询条件获取项目列表。
     *
     * @param queryDto 查询条件。
     * @return 包含匹配项目的分页结果。
     */
    PageVO<ProjectVo> selectProject(ProjectQueryDto queryDto);

    List<ProjectVo> selectProject(List<String> projectIds, List<String> areaIds);

    /**
     * 根据项目ID获取项目详情。
     *
     * @param id 项目ID。
     * @return 包含项目详情的对象。
     */
    ProjectEntity getDetail(String id);

    /**
     * 根据项目名称和地区ID查询项目列表。
     *
     * @param name 项目名称。
     * @param areaId 地区ID。
     * @return 包含匹配项目的列表。
     */
    List<ProjectEntity> selectByNameAndAreaId(String name, String areaId);

    /**
     * 保存或更新项目信息。
     *
     * @param projectEntity 项目实体。
     * @param isUpdate 是否为更新操作。
     * @return 操作影响的行数。
     */
    int saveOrUpdate(ProjectEntity projectEntity, boolean isUpdate);

    /**
     * 根据项目ID获取项目信息。
     *
     * @param id 项目ID。
     * @return 包含项目信息的对象。
     */
    ProjectEntity selectProjectById(String id);

    /**
     * 删除项目。
     *
     * @param id 项目ID。
     * @return 操作影响的行数。
     */
    int deleteProject(String id);

    /**
     * 根据项目ID列表查询项目列表。
     *
     * @param projectIds 项目ID列表。
     * @return 包含匹配项目的列表。
     */
    List<ProjectEntity> selectProjectByIds(List<String> projectIds);

    /**
     * 根据关键字和地区ID列表查询项目列表。
     *
     * @param areaIds 地区ID列表。
     * @param keyword 关键字。
     * @return 包含匹配项目的列表。
     */
    List<ProjectEntity> selectProjectByKeyword(List<String> areaIds, String keyword);

    /**
     * 查询所有项目
     *
     * @return
     */
    List<ProjectEntity> selectAll();

    List<ProjectEntity> selectByAreaId(String areaId);

    List<String> selectProjectByAreaIds(List<String> stringList);
}
/* Ended by AICoder, pid:t02cdrd1ceo70921448e0a1210d5ef8e14e892c2 */