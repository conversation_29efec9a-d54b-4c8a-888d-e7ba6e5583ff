/* Started by AICoder, pid:y9f65413526698e1454a0a8d80157816ade6e1fd */
package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.interfaces.web.dto.BiddingConclusionDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionDropDownVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BiddingConclusionVo;

public interface BiddingConclusionDomainService {

    /**
     * 获取投标结论下拉框数据
     *
     * @return BiddingConclusionDropDownVo.
     */
    BiddingConclusionDropDownVo queryDropDown();

    /**
     * 根据商机id查询结论是否是首次填写
     * */
    boolean getFirstFill(String projectId);

    /**
     *  根据商机id查询投标结论详情
     * */
    BiddingConclusionVo getDetail(String projectId);

    /**
     *  新增或更新投标结论信息
     * */
    void addOrUpdateBiddingConclusion(BiddingConclusionDto conclusionDto);
}

/* Ended by AICoder, pid:y9f65413526698e1454a0a8d80157816ade6e1fd */