/* Started by AICoder, pid:8c6b0ra76b2df1b14c08092fd00d09423118a698 */
package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.domain.aggregate.model.ProjectAreaObj;
import com.zte.uedm.dcdigital.interfaces.web.dto.ProjectAreaQueryDto;

import java.util.List;

/**
 * 项目区域仓库接口，定义了对项目区域对象的基本操作。
 */
public interface ProjectAreaRepository {

    /**
     * 根据ID查询项目区域对象。
     *
     * @param id 项目区域的唯一标识符
     * @return 匹配的项目区域对象，如果没有找到则返回null
     */
    ProjectAreaObj selectById(String id);

    ProjectAreaObj selectByName(String name, Integer areaLevel);

    List<ProjectAreaObj> selectByParentId(String parentId);

    /**
     * 根据名称查询项目区域对象，并支持分页。
     *
     * @param queryDto 查询条件，包含名称和分页信息
     * @return 包含匹配项目区域对象的分页结果
     */
    PageVO<ProjectAreaObj> queryByCondition(ProjectAreaQueryDto queryDto);

    /**
     * 插入一个新的项目区域对象。
     *
     * @param projectAreaObj 要插入的项目区域对象
     */
    void insert(ProjectAreaObj projectAreaObj);

    /**
     * 更新一个现有的项目区域对象。
     *
     * @param projectAreaObj 要更新的项目区域对象
     */
    void update(ProjectAreaObj projectAreaObj);

    /**
     * 根据ID删除一个项目区域对象。
     *
     * @param id 要删除的项目区域对象的唯一标识符
     */
    void deleteById(String id);

    List<ProjectAreaObj> selectAreaListBy(List<String> areaIds, String areaName);

    List<String> selectAllAreaIds(String areaId);

    List<ProjectAreaObj> selectAreaListByName(String areaName);

    /**
     * 查询所有地区信息
     *
     * @return 所有地区对象列表
     */
    List<ProjectAreaObj> selectAllAreas();
}
/* Ended by AICoder, pid:8c6b0ra76b2df1b14c08092fd00d09423118a698 */