/* Started by AICoder, pid:q8049d8fffc3b5c14ed6089520a32c2023a8c526 */
package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatYearEntity;

import java.util.List;

public interface PageStatYearRepository {
    /**
     * 添加用户页面访问年统计记录
     *
     * @param yearEntityList 用户页面访问年统计实体列表对象
     */
    void addPageStatYearList(List<PageStatYearEntity> yearEntityList);

    /**
     * 查询指定日期的用户页面访问年统计记录
     *
     * @param yearEntity 查询条件
     * @return 用户页面访问年统计记录列表
     */
    List<PageStatYearEntity> getPageStatYearList(PageStatYearEntity yearEntity);

    /**
     * 更新用户页面访问年统计记录
     *
     * @param yearEntityList 用户页面访问年统计实体列表对象
     */
    void updatePageStatYearList(List<PageStatYearEntity> yearEntityList);

    void deleteByYearNumber(Integer yearNumber);
}

/* Ended by AICoder, pid:q8049d8fffc3b5c14ed6089520a32c2023a8c526 */