package com.zte.uedm.dcdigital.domain.service.impl;

import com.alibaba.excel.EasyExcel;
import com.zte.uedm.dcdigital.common.bean.project.BusinessStatisticsVo;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetWeekEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetYearEntity;
import com.zte.uedm.dcdigital.domain.service.BusinessAssetDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessAssetMapper;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessAssetExportVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessAssetStatVo;
import com.zte.uedm.dcdigital.sdk.project.dto.BusinessStatisticsQueryDto;
import com.zte.uedm.dcdigital.sdk.project.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商机资产领域服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusinessAssetDomainServiceImpl implements BusinessAssetDomainService {

    @Autowired
    private BusinessAssetMapper businessAssetMapper;

    @Autowired
    private ProjectService projectService;

    @Override
    public BusinessAssetStatVo getStatBusiness(BusinessAssetQueryDto queryDto) {
        log.info("Getting business asset statistics, queryDto: {}", queryDto);
        
        BusinessAssetStatVo result = new BusinessAssetStatVo();

        // 判断天表是否有数据，决定调用project服务的参数
        BusinessStatisticsQueryDto businessStatisticsQueryDto = new BusinessStatisticsQueryDto();

        // 检查天表中是否有任何数据
        boolean hasAnyDayData = hasAnyDataInDayTable();

        if (!hasAnyDayData) {
            // 天表中没有任何数据，不传开始时间（从最早开始获取）
            log.info("Day table is empty, querying from project without start time");
            businessStatisticsQueryDto.setStartDate(null);
            //获取当前时间并格式化为yyyyMMdd
            LocalDateTime now = LocalDateTime.now();
            String endDate = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            businessStatisticsQueryDto.setEndDate(endDate);
        } else {
            // 天表中有数据，不传结束时间（获取最新数据）
            log.info("Day table has data, querying from project without end time");
            businessStatisticsQueryDto.setStartDate(queryDto.getStartTime());
            businessStatisticsQueryDto.setEndDate(null);
        }

        // 从project模块获取商机统计数据
        List<BusinessStatisticsVo> areaBusinessStatistics = projectService.getAreaBusinessStatistics(businessStatisticsQueryDto);

        // 将获取到的数据入库到天表
        if (areaBusinessStatistics != null && !areaBusinessStatistics.isEmpty()) {
            saveBusinessStatisticsToDay(areaBusinessStatistics);
        }

        try {
            // 根据时间类型查询不同的表
            switch (queryDto.getTimeType()) {
                case 1: // 天
                    result = getBusinessStatByDay(queryDto);
                    break;
                case 2: // 周
                    result = getBusinessStatByWeek(queryDto);
                    break;
                case 3: // 月
                    result = getBusinessStatByMonth(queryDto);
                    break;
                case 4: // 年
                    result = getBusinessStatByYear(queryDto);
                    break;
                default:
                    log.warn("Unsupported time type: {}", queryDto.getTimeType());
                    result.setTimeStatList(new ArrayList<>());
                    result.setAreaStatList(new ArrayList<>());
            }
            
            log.info("Business asset statistics completed successfully");
            return result;
            
        } catch (Exception e) {
            log.error("Error getting business asset statistics", e);
            result.setTimeStatList(new ArrayList<>());
            result.setAreaStatList(new ArrayList<>());
            return result;
        }
    }

    /**
     * 按天查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByDay(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();

        // 1. 生成时间范围内每个时间节点的汇总数据
        List<BusinessAssetStatVo.TimeStatData> timeStatList = generateTimeStatData(queryDto, 1);
        result.setTimeStatList(timeStatList);

        // 2. 查询下级地区数据（基于时间点）
        List<BusinessAssetStatVo.AreaStatData> areaStatList = getAreaStatData(queryDto, 1);
        result.setAreaStatList(areaStatList);

        return result;
    }

    /**
     * 按周查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByWeek(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();

        // 1. 生成时间范围内每个时间节点的汇总数据
        List<BusinessAssetStatVo.TimeStatData> timeStatList = generateTimeStatData(queryDto, 2);
        result.setTimeStatList(timeStatList);

        // 2. 查询下级地区数据（基于时间点）
        List<BusinessAssetStatVo.AreaStatData> areaStatList = getAreaStatData(queryDto, 2);
        result.setAreaStatList(areaStatList);

        return result;
    }

    /**
     * 按月查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByMonth(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();

        // 1. 生成时间范围内每个时间节点的汇总数据
        List<BusinessAssetStatVo.TimeStatData> timeStatList = generateTimeStatData(queryDto, 3);
        result.setTimeStatList(timeStatList);

        // 2. 查询下级地区数据（基于时间点）
        List<BusinessAssetStatVo.AreaStatData> areaStatList = getAreaStatData(queryDto, 3);
        result.setAreaStatList(areaStatList);

        return result;
    }

    /**
     * 按年查询商机统计数据
     */
    private BusinessAssetStatVo getBusinessStatByYear(BusinessAssetQueryDto queryDto) {
        BusinessAssetStatVo result = new BusinessAssetStatVo();

        // 1. 生成时间范围内每个时间节点的汇总数据
        List<BusinessAssetStatVo.TimeStatData> timeStatList = generateTimeStatData(queryDto, 4);
        result.setTimeStatList(timeStatList);

        // 2. 查询下级地区数据（基于时间点）
        List<BusinessAssetStatVo.AreaStatData> areaStatList = getAreaStatData(queryDto, 4);
        result.setAreaStatList(areaStatList);

        return result;
    }

    /**
     * 生成时间范围内每个时间节点的汇总数据，没数据默认补0
     */
    private List<BusinessAssetStatVo.TimeStatData> generateTimeStatData(BusinessAssetQueryDto queryDto, Integer timeType) {
        log.info("Generating time stat data for parentAreaId: {}, timeType: {}", queryDto.getAreaId(), timeType);

        List<BusinessAssetStatVo.TimeStatData> timeStatList = new ArrayList<>();

        try {
            // 1. 生成时间范围内所有时间节点
            List<String> timeNodes = generateTimeNodes(queryDto.getStartTime(), queryDto.getEndTime(), timeType);

            // 2. 查询实际数据
            Map<String, BusinessAssetStatVo.TimeStatData> actualDataMap = getActualTimeStatData(queryDto, timeType);

            // 3. 填充数据，没有数据的节点补0
            for (String timeNode : timeNodes) {
                BusinessAssetStatVo.TimeStatData timeStatData = actualDataMap.get(timeNode);
                if (timeStatData == null) {
                    // 没有数据，创建默认数据（补0）
                    timeStatData = new BusinessAssetStatVo.TimeStatData();
                    timeStatData.setTimeNode(timeNode);
                    timeStatData.setProjectAddNum(0L);
                    timeStatData.setProjectStartNum(0L);
                }
                timeStatList.add(timeStatData);
            }

            log.info("Generated {} time stat data entries", timeStatList.size());

        } catch (Exception e) {
            log.error("Error generating time stat data", e);
        }

        return timeStatList;
    }

    /**
     * 获取下级地区数据
     */
    private List<BusinessAssetStatVo.AreaStatData> getAreaStatData(BusinessAssetQueryDto queryDto, Integer timeType) {
        log.info("Getting area stat data for parentAreaId: {}, timePoint: {}", queryDto.getAreaId(), queryDto.getTimePoint());

        List<BusinessAssetStatVo.AreaStatData> areaStatList = new ArrayList<>();

        try {
            // 验证时间点参数
            if (queryDto.getTimePoint() == null || queryDto.getTimePoint().trim().isEmpty()) {
                log.warn("Time point is null or empty, using end time as default");
                queryDto.setTimePoint(queryDto.getEndTime());
            }

            // 根据时间类型查询不同的表
            List<? extends Object> childDataList = null;
            switch (timeType) {
                case 1: // 天
                    childDataList = businessAssetMapper.selectChildAreaDayDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                case 2: // 周
                    childDataList = businessAssetMapper.selectChildAreaWeekDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                case 3: // 月
                    childDataList = businessAssetMapper.selectChildAreaMonthDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                case 4: // 年
                    childDataList = businessAssetMapper.selectChildAreaYearDataByTimePoint(queryDto.getAreaId(), queryDto.getTimePoint());
                    break;
                default:
                    log.warn("Unsupported time type: {}", timeType);
                    return areaStatList;
            }

            // 汇总数据，用于生成ALL行
            BusinessAssetStatVo.AreaStatData allData = new BusinessAssetStatVo.AreaStatData();
            allData.setAreaName("ALL");
            allData.setAllNum(0L);
            allData.setProjectAddNum(0L);
            allData.setBidNum(0L);
            allData.setProjectStartNum(0L);
            allData.setSubBidNum(0L);
            allData.setBeforeBidNum(0L);
            allData.setProjectApprovalNum(0L);

            // 处理子节点数据
            if (childDataList != null && !childDataList.isEmpty()) {
                for (Object obj : childDataList) {
                    BusinessAssetStatVo.AreaStatData areaData = convertToAreaStatData(obj);
                    if (areaData != null) {
                        areaStatList.add(areaData);

                        // 累加到汇总数据
                        allData.setAllNum(allData.getAllNum() + (areaData.getAllNum() != null ? areaData.getAllNum() : 0L));
                        allData.setProjectAddNum(allData.getProjectAddNum() + (areaData.getProjectAddNum() != null ? areaData.getProjectAddNum() : 0L));
                        allData.setBidNum(allData.getBidNum() + (areaData.getBidNum() != null ? areaData.getBidNum() : 0L));
                        allData.setProjectStartNum(allData.getProjectStartNum() + (areaData.getProjectStartNum() != null ? areaData.getProjectStartNum() : 0L));
                        allData.setSubBidNum(allData.getSubBidNum() + (areaData.getSubBidNum() != null ? areaData.getSubBidNum() : 0L));
                        allData.setBeforeBidNum(allData.getBeforeBidNum() + (areaData.getBeforeBidNum() != null ? areaData.getBeforeBidNum() : 0L));
                        allData.setProjectApprovalNum(allData.getProjectApprovalNum() + (areaData.getProjectApprovalNum() != null ? areaData.getProjectApprovalNum() : 0L));
                    }
                }
            }

            // 将汇总行添加到列表开头
            areaStatList.add(0, allData);

            log.info("Area stat data query completed, found {} child areas plus 1 summary row", areaStatList.size() - 1);

        } catch (Exception e) {
            log.error("Error getting area stat data", e);
        }

        return areaStatList;
    }

    /**
     * 生成时间范围内的所有时间节点
     *
     * @param startTime 开始时间，格式根据timeType而定：
     *                  - timeType=1(天): yyyyMMdd (如: 20250105)
     *                  - timeType=2(周): yyyyww (如: 202530)
     *                  - timeType=3(月): yyyyMM (如: 202505)
     *                  - timeType=4(年): yyyy (如: 2025)
     * @param endTime 结束时间，格式同startTime
     * @param timeType 时间类型：1-天，2-周，3-月，4-年
     * @return 时间节点列表，输出格式与输入格式相同
     */
    private List<String> generateTimeNodes(String startTime, String endTime, Integer timeType) {
        List<String> timeNodes = new ArrayList<>();

        try {
            switch (timeType) {
                case 1: // 天 - 输入输出格式：yyyyMMdd
                    timeNodes = generateDayNodes(startTime, endTime);
                    break;
                case 2: // 周 - 输入输出格式：yyyyww
                    timeNodes = generateWeekNodes(startTime, endTime);
                    break;
                case 3: // 月 - 输入输出格式：yyyyMM
                    timeNodes = generateMonthNodes(startTime, endTime);
                    break;
                case 4: // 年 - 输入输出格式：yyyy
                    timeNodes = generateYearNodes(startTime, endTime);
                    break;
                default:
                    log.warn("Unsupported time type for generating time nodes: {}", timeType);
            }
        } catch (Exception e) {
            log.error("Error generating time nodes with startTime: {}, endTime: {}, timeType: {}", startTime, endTime, timeType, e);
        }

        return timeNodes;
    }

    /**
     * 生成日期节点（输入格式：yyyyMMdd，输出格式：yyyyMMdd）
     */
    private List<String> generateDayNodes(String startTime, String endTime) {
        List<String> dayNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（天格式：yyyyMMdd）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 1);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 1);

            log.debug("Generating day nodes from {} to {}", formattedStartTime, formattedEndTime);

            LocalDate start = LocalDate.parse(formattedStartTime, DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDate end = LocalDate.parse(formattedEndTime, DateTimeFormatter.ofPattern("yyyyMMdd"));

            LocalDate current = start;
            while (!current.isAfter(end)) {
                dayNodes.add(current.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                current = current.plusDays(1);
            }

            log.debug("Generated {} day nodes", dayNodes.size());
        } catch (Exception e) {
            log.error("Error generating day nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return dayNodes;
    }

    /**
     * 验证和格式化时间输入（根据timeType使用不同格式）
     * @param timeInput 输入的时间字符串
     * @param timeType 时间类型：1-天，2-周，3-月，4-年
     * @return 格式化后的时间字符串
     */
    private String validateAndFormatTimeInput(String timeInput, Integer timeType) {
        if (timeInput == null || timeInput.trim().isEmpty()) {
            throw new IllegalArgumentException("Time input cannot be null or empty");
        }

        String trimmedInput = timeInput.trim();
        // 移除所有非数字字符
        String numericInput = trimmedInput.replaceAll("[^0-9]", "");

        switch (timeType) {
            case 1: // 天 - 期望格式：yyyyMMdd (8位)
                return validateDayFormat(numericInput, timeInput);
            case 2: // 周 - 期望格式：yyyyww (6位)
                return validateWeekFormat(numericInput, timeInput);
            case 3: // 月 - 期望格式：yyyyMM (6位)
                return validateMonthFormat(numericInput, timeInput);
            case 4: // 年 - 期望格式：yyyy (4位)
                return validateYearFormat(numericInput, timeInput);
            default:
                throw new IllegalArgumentException("Unsupported time type: " + timeType);
        }
    }

    /**
     * 验证天格式：yyyyMMdd
     */
    private String validateDayFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 8) {
            try {
                LocalDate.parse(numericInput, DateTimeFormatter.ofPattern("yyyyMMdd"));
                return numericInput;
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid day format: " + originalInput + ", expected yyyyMMdd");
            }
        } else {
            throw new IllegalArgumentException("Invalid day format: " + originalInput + ", expected yyyyMMdd (8 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 验证周格式：yyyyww
     */
    private String validateWeekFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 6) {
            try {
                int year = Integer.parseInt(numericInput.substring(0, 4));
                int week = Integer.parseInt(numericInput.substring(4, 6));
                if (year < 1900 || year > 2100) {
                    throw new IllegalArgumentException("Year out of valid range: " + year);
                }
                if (week < 1 || week > 53) {
                    throw new IllegalArgumentException("Week out of valid range: " + week);
                }
                return numericInput;
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid week format: " + originalInput + ", expected yyyyww");
            }
        } else {
            throw new IllegalArgumentException("Invalid week format: " + originalInput + ", expected yyyyww (6 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 验证月格式：yyyyMM
     */
    private String validateMonthFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 6) {
            try {
                LocalDate.parse(numericInput + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                return numericInput;
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid month format: " + originalInput + ", expected yyyyMM");
            }
        } else {
            throw new IllegalArgumentException("Invalid month format: " + originalInput + ", expected yyyyMM (6 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 验证年格式：yyyy
     */
    private String validateYearFormat(String numericInput, String originalInput) {
        if (numericInput.length() == 4) {
            try {
                int year = Integer.parseInt(numericInput);
                if (year < 1900 || year > 2100) {
                    throw new IllegalArgumentException("Year out of valid range: " + year);
                }
                return numericInput;
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid year format: " + originalInput + ", expected yyyy");
            }
        } else {
            throw new IllegalArgumentException("Invalid year format: " + originalInput + ", expected yyyy (4 digits), got " + numericInput.length() + " digits");
        }
    }

    /**
     * 生成周节点（输入格式：yyyyww，输出格式：yyyyww）
     */
    private List<String> generateWeekNodes(String startTime, String endTime) {
        List<String> weekNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（周格式：yyyyww）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 2);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 2);

            log.debug("Generating week nodes from {} to {}", formattedStartTime, formattedEndTime);

            // 解析周格式：yyyyww
            int startYear = Integer.parseInt(formattedStartTime.substring(0, 4));
            int startWeek = Integer.parseInt(formattedStartTime.substring(4, 6));
            int endYear = Integer.parseInt(formattedEndTime.substring(0, 4));
            int endWeek = Integer.parseInt(formattedEndTime.substring(4, 6));

            // 生成周范围内的所有周节点
            int currentYear = startYear;
            int currentWeek = startWeek;

            while (currentYear < endYear || (currentYear == endYear && currentWeek <= endWeek)) {
                String weekStr = String.format("%04d%02d", currentYear, currentWeek);
                weekNodes.add(weekStr);

                currentWeek++;
                // 检查是否需要进入下一年
                if (currentWeek > 52) { // 简化处理，大部分年份有52周
                    currentYear++;
                    currentWeek = 1;
                }
            }

            log.debug("Generated {} week nodes", weekNodes.size());
        } catch (Exception e) {
            log.error("Error generating week nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return weekNodes;
    }

    /**
     * 生成月节点（输入格式：yyyyMM，输出格式：yyyyMM）
     */
    private List<String> generateMonthNodes(String startTime, String endTime) {
        List<String> monthNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（月格式：yyyyMM）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 3);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 3);

            log.debug("Generating month nodes from {} to {}", formattedStartTime, formattedEndTime);

            // 解析月格式：yyyyMM
            LocalDate start = LocalDate.parse(formattedStartTime + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDate end = LocalDate.parse(formattedEndTime + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 从开始月份开始生成
            LocalDate current = start;

            while (!current.isAfter(end)) {
                monthNodes.add(current.format(DateTimeFormatter.ofPattern("yyyyMM")));
                current = current.plusMonths(1);
            }

            log.debug("Generated {} month nodes", monthNodes.size());
        } catch (Exception e) {
            log.error("Error generating month nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return monthNodes;
    }

    /**
     * 生成年节点（输入格式：yyyy，输出格式：yyyy）
     */
    private List<String> generateYearNodes(String startTime, String endTime) {
        List<String> yearNodes = new ArrayList<>();

        try {
            // 验证和格式化输入时间（年格式：yyyy）
            String formattedStartTime = validateAndFormatTimeInput(startTime, 4);
            String formattedEndTime = validateAndFormatTimeInput(endTime, 4);

            log.debug("Generating year nodes from {} to {}", formattedStartTime, formattedEndTime);

            // 解析年格式：yyyy
            int startYear = Integer.parseInt(formattedStartTime);
            int endYear = Integer.parseInt(formattedEndTime);

            // 生成年范围内的所有年节点
            for (int year = startYear; year <= endYear; year++) {
                yearNodes.add(String.valueOf(year));
            }

            log.debug("Generated {} year nodes", yearNodes.size());
        } catch (Exception e) {
            log.error("Error generating year nodes with startTime: {}, endTime: {}", startTime, endTime, e);
        }

        return yearNodes;
    }

    /**
     * 获取实际的时间统计数据
     */
    private Map<String, BusinessAssetStatVo.TimeStatData> getActualTimeStatData(BusinessAssetQueryDto queryDto, Integer timeType) {
        Map<String, BusinessAssetStatVo.TimeStatData> dataMap = new HashMap<>();

        try {
            List<? extends Object> entities = null;
            switch (timeType) {
                case 1: // 天
                    entities = businessAssetMapper.selectChildAreaDayDataByTimeRange(queryDto.getAreaId(), queryDto.getStartTime(), queryDto.getEndTime());
                    break;
                case 2: // 周
                    entities = businessAssetMapper.selectChildAreaWeekDataByTimeRange(queryDto.getAreaId(), queryDto.getStartTime(), queryDto.getEndTime());
                    break;
                case 3: // 月
                    entities = businessAssetMapper.selectChildAreaMonthDataByTimeRange(queryDto.getAreaId(), queryDto.getStartTime(), queryDto.getEndTime());
                    break;
                case 4: // 年
                    entities = businessAssetMapper.selectChildAreaYearDataByTimeRange(queryDto.getAreaId(), queryDto.getStartTime(), queryDto.getEndTime());
                    break;
                default:
                    log.warn("Unsupported time type: {}", timeType);
                    return dataMap;
            }

            if (entities != null) {
                for (Object entity : entities) {
                    BusinessAssetStatVo.TimeStatData timeStatData = convertToTimeStatData(entity);
                    if (timeStatData != null) {
                        dataMap.put(timeStatData.getTimeNode(), timeStatData);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error getting actual time stat data", e);
        }

        return dataMap;
    }

    /**
     * 将Entity转换为TimeStatData
     */
    private BusinessAssetStatVo.TimeStatData convertToTimeStatData(Object entity) {
        try {
            BusinessAssetStatVo.TimeStatData timeStatData = new BusinessAssetStatVo.TimeStatData();

            if (entity instanceof BusinessAssetDayEntity) {
                BusinessAssetDayEntity dayEntity = (BusinessAssetDayEntity) entity;
                timeStatData.setTimeNode(dayEntity.getDay());
                timeStatData.setProjectAddNum(dayEntity.getProjectAddNum());
                timeStatData.setProjectStartNum(dayEntity.getProjectStartNum());
            } else if (entity instanceof BusinessAssetWeekEntity) {
                BusinessAssetWeekEntity weekEntity = (BusinessAssetWeekEntity) entity;
                timeStatData.setTimeNode(weekEntity.getDay());
                timeStatData.setProjectAddNum(weekEntity.getProjectAddNum());
                timeStatData.setProjectStartNum(weekEntity.getProjectStartNum());
            } else if (entity instanceof BusinessAssetMonthEntity) {
                BusinessAssetMonthEntity monthEntity = (BusinessAssetMonthEntity) entity;
                timeStatData.setTimeNode(monthEntity.getDay());
                timeStatData.setProjectAddNum(monthEntity.getProjectAddNum());
                timeStatData.setProjectStartNum(monthEntity.getProjectStartNum());
            } else if (entity instanceof BusinessAssetYearEntity) {
                BusinessAssetYearEntity yearEntity = (BusinessAssetYearEntity) entity;
                timeStatData.setTimeNode(yearEntity.getDay());
                timeStatData.setProjectAddNum(yearEntity.getProjectAddNum());
                timeStatData.setProjectStartNum(yearEntity.getProjectStartNum());
            } else {
                log.warn("Unknown entity type: {}", entity.getClass().getSimpleName());
                return null;
            }

            return timeStatData;

        } catch (Exception e) {
            log.error("Error converting entity to time stat data", e);
            return null;
        }
    }

    /**
     * 将不同类型的Entity转换为AreaStatData
     */
    private BusinessAssetStatVo.AreaStatData convertToAreaStatData(Object entity) {
        try {
            BusinessAssetStatVo.AreaStatData areaStatData = new BusinessAssetStatVo.AreaStatData();

            if (entity instanceof BusinessAssetDayEntity) {
                BusinessAssetDayEntity dayEntity = (BusinessAssetDayEntity) entity;
                areaStatData.setAreaName(getAreaName(dayEntity.getAreaId()));
                areaStatData.setAllNum(dayEntity.getAllNum());
                areaStatData.setProjectAddNum(dayEntity.getProjectAddNum());
                areaStatData.setBidNum(dayEntity.getBidNum());
                areaStatData.setProjectStartNum(dayEntity.getProjectStartNum());
                areaStatData.setSubBidNum(dayEntity.getSubBidNum());
                areaStatData.setBeforeBidNum(dayEntity.getBeforeBidNum());
                areaStatData.setProjectApprovalNum(dayEntity.getProjectApprovalNum());
            } else if (entity instanceof BusinessAssetWeekEntity) {
                BusinessAssetWeekEntity weekEntity = (BusinessAssetWeekEntity) entity;
                areaStatData.setAreaName(getAreaName(weekEntity.getAreaId()));
                areaStatData.setAllNum(weekEntity.getAllNum());
                areaStatData.setProjectAddNum(weekEntity.getProjectAddNum());
                areaStatData.setBidNum(weekEntity.getBidNum());
                areaStatData.setProjectStartNum(weekEntity.getProjectStartNum());
                areaStatData.setSubBidNum(weekEntity.getSubBidNum());
                areaStatData.setBeforeBidNum(weekEntity.getBeforeBidNum());
                areaStatData.setProjectApprovalNum(weekEntity.getProjectApprovalNum());
            } else if (entity instanceof BusinessAssetMonthEntity) {
                BusinessAssetMonthEntity monthEntity = (BusinessAssetMonthEntity) entity;
                areaStatData.setAreaName(getAreaName(monthEntity.getAreaId()));
                areaStatData.setAllNum(monthEntity.getAllNum());
                areaStatData.setProjectAddNum(monthEntity.getProjectAddNum());
                areaStatData.setBidNum(monthEntity.getBidNum());
                areaStatData.setProjectStartNum(monthEntity.getProjectStartNum());
                areaStatData.setSubBidNum(monthEntity.getSubBidNum());
                areaStatData.setBeforeBidNum(monthEntity.getBeforeBidNum());
                areaStatData.setProjectApprovalNum(monthEntity.getProjectApprovalNum());
            } else if (entity instanceof BusinessAssetYearEntity) {
                BusinessAssetYearEntity yearEntity = (BusinessAssetYearEntity) entity;
                areaStatData.setAreaName(getAreaName(yearEntity.getAreaId()));
                areaStatData.setAllNum(yearEntity.getAllNum());
                areaStatData.setProjectAddNum(yearEntity.getProjectAddNum());
                areaStatData.setBidNum(yearEntity.getBidNum());
                areaStatData.setProjectStartNum(yearEntity.getProjectStartNum());
                areaStatData.setSubBidNum(yearEntity.getSubBidNum());
                areaStatData.setBeforeBidNum(yearEntity.getBeforeBidNum());
                areaStatData.setProjectApprovalNum(yearEntity.getProjectApprovalNum());
            } else {
                log.warn("Unknown entity type: {}", entity.getClass().getSimpleName());
                return null;
            }

            return areaStatData;

        } catch (Exception e) {
            log.error("Error converting entity to junior data", e);
            return null;
        }
    }

    /**
     * 获取地区名称
     */
    private String getAreaName(String areaId) {
        try {
            String areaName = businessAssetMapper.selectAreaNameById(areaId);
            return areaName != null ? areaName : "Unknown Area";
        } catch (Exception e) {
            log.warn("Failed to get area name for areaId: {}", areaId);
            return "Unknown Area";
        }
    }

    @Override
    public void aggregateBusinessAssetData() {
        log.info("Starting business asset data aggregation");

        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime yesterday = now.minusDays(1);

            String yesterdayStr = yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String weekStr = yesterday.format(DateTimeFormatter.ofPattern("yyyyww"));
            String monthStr = yesterday.format(DateTimeFormatter.ofPattern("yyyyMM"));
            String yearStr = yesterday.format(DateTimeFormatter.ofPattern("yyyy"));

            // 汇聚周数据
            aggregateWeekData(yesterdayStr, weekStr);

            // 汇聚月数据
            aggregateMonthData(yesterdayStr, monthStr);

            // 汇聚年数据
            aggregateYearData(yesterdayStr, yearStr);

            log.info("Business asset data aggregation completed for date: {}", yesterdayStr);

        } catch (Exception e) {
            log.error("Error during business asset data aggregation", e);
        }
    }

    /**
     * 汇聚周数据
     */
    private void aggregateWeekData(String day, String week) {
        try {
            log.info("Aggregating week data for week: {}", week);

            // 计算周的开始和结束日期
            String weekStart = calculateWeekStart(day);
            String weekEnd = calculateWeekEnd(day);

            // 查询该周的所有日表数据
            List<BusinessAssetDayEntity> dayDataList = businessAssetMapper.selectDayDateByTimeRange(weekStart, weekEnd);

            // 按地区分组汇聚数据
            Map<String, BusinessAssetWeekEntity> weekDataMap = new HashMap<>();

            for (BusinessAssetDayEntity dayEntity : dayDataList) {
                String areaId = dayEntity.getAreaId();
                BusinessAssetWeekEntity weekEntity = weekDataMap.get(areaId);

                if (weekEntity == null) {
                    weekEntity = new BusinessAssetWeekEntity();
                    weekEntity.setId(generateId(areaId, week));
                    weekEntity.setDay(week);
                    weekEntity.setAreaId(areaId);
                    weekEntity.setParentAreaId(dayEntity.getParentAreaId()); // 设置父节点ID
                    weekEntity.setAllNum(0L);
                    weekEntity.setBidNum(0L);
                    weekEntity.setSubBidNum(0L);
                    weekEntity.setBeforeBidNum(0L);
                    weekEntity.setProjectApprovalNum(0L);
                    weekEntity.setProjectAddNum(0L);
                    weekEntity.setProjectStartNum(0L);
                    weekEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    weekDataMap.put(areaId, weekEntity);
                }

                // 汇聚数据
                weekEntity.setAllNum(weekEntity.getAllNum() + (dayEntity.getAllNum() != null ? dayEntity.getAllNum() : 0L));
                weekEntity.setBidNum(weekEntity.getBidNum() + (dayEntity.getBidNum() != null ? dayEntity.getBidNum() : 0L));
                weekEntity.setSubBidNum(weekEntity.getSubBidNum() + (dayEntity.getSubBidNum() != null ? dayEntity.getSubBidNum() : 0L));
                weekEntity.setBeforeBidNum(weekEntity.getBeforeBidNum() + (dayEntity.getBeforeBidNum() != null ? dayEntity.getBeforeBidNum() : 0L));
                weekEntity.setProjectApprovalNum(weekEntity.getProjectApprovalNum() + (dayEntity.getProjectApprovalNum() != null ? dayEntity.getProjectApprovalNum() : 0L));
                weekEntity.setProjectAddNum(weekEntity.getProjectAddNum() + (dayEntity.getProjectAddNum() != null ? dayEntity.getProjectAddNum() : 0L));
                weekEntity.setProjectStartNum(weekEntity.getProjectStartNum() + (dayEntity.getProjectStartNum() != null ? dayEntity.getProjectStartNum() : 0L));
            }

            // 批量插入或更新周表数据
            if (!weekDataMap.isEmpty()) {
                List<BusinessAssetWeekEntity> weekDataList = new ArrayList<>(weekDataMap.values());
                businessAssetMapper.batchInsertWeekData(weekDataList);
                log.info("Aggregated {} week records for week: {}", weekDataList.size(), week);
            }

        } catch (Exception e) {
            log.error("Error aggregating week data for week: {}", week, e);
        }
    }

    /**
     * 汇聚月数据
     */
    private void aggregateMonthData(String day, String month) {
        try {
            log.info("Aggregating month data for month: {}", month);

            // 计算月的开始和结束日期
            String monthStart = month + "01";
            String monthEnd = calculateMonthEnd(month);

            // 查询该月的所有日表数据
            List<BusinessAssetDayEntity> dayDataList = businessAssetMapper.selectDayDateByTimeRange(monthStart, monthEnd);

            // 按地区分组汇聚数据
            Map<String, BusinessAssetMonthEntity> monthDataMap = new HashMap<>();

            for (BusinessAssetDayEntity dayEntity : dayDataList) {
                String areaId = dayEntity.getAreaId();
                BusinessAssetMonthEntity monthEntity = monthDataMap.get(areaId);

                if (monthEntity == null) {
                    monthEntity = new BusinessAssetMonthEntity();
                    monthEntity.setId(generateId(areaId, month));
                    monthEntity.setDay(month);
                    monthEntity.setAreaId(areaId);
                    monthEntity.setParentAreaId(dayEntity.getParentAreaId()); // 设置父节点ID
                    monthEntity.setAllNum(0L);
                    monthEntity.setBidNum(0L);
                    monthEntity.setSubBidNum(0L);
                    monthEntity.setBeforeBidNum(0L);
                    monthEntity.setProjectApprovalNum(0L);
                    monthEntity.setProjectAddNum(0L);
                    monthEntity.setProjectStartNum(0L);
                    monthEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    monthDataMap.put(areaId, monthEntity);
                }

                // 汇聚数据
                monthEntity.setAllNum(monthEntity.getAllNum() + (dayEntity.getAllNum() != null ? dayEntity.getAllNum() : 0L));
                monthEntity.setBidNum(monthEntity.getBidNum() + (dayEntity.getBidNum() != null ? dayEntity.getBidNum() : 0L));
                monthEntity.setSubBidNum(monthEntity.getSubBidNum() + (dayEntity.getSubBidNum() != null ? dayEntity.getSubBidNum() : 0L));
                monthEntity.setBeforeBidNum(monthEntity.getBeforeBidNum() + (dayEntity.getBeforeBidNum() != null ? dayEntity.getBeforeBidNum() : 0L));
                monthEntity.setProjectApprovalNum(monthEntity.getProjectApprovalNum() + (dayEntity.getProjectApprovalNum() != null ? dayEntity.getProjectApprovalNum() : 0L));
                monthEntity.setProjectAddNum(monthEntity.getProjectAddNum() + (dayEntity.getProjectAddNum() != null ? dayEntity.getProjectAddNum() : 0L));
                monthEntity.setProjectStartNum(monthEntity.getProjectStartNum() + (dayEntity.getProjectStartNum() != null ? dayEntity.getProjectStartNum() : 0L));
            }

            // 批量插入或更新月表数据
            if (!monthDataMap.isEmpty()) {
                List<BusinessAssetMonthEntity> monthDataList = new ArrayList<>(monthDataMap.values());
                businessAssetMapper.batchInsertMonthData(monthDataList);
                log.info("Aggregated {} month records for month: {}", monthDataList.size(), month);
            }

        } catch (Exception e) {
            log.error("Error aggregating month data for month: {}", month, e);
        }
    }

    /**
     * 汇聚年数据
     */
    private void aggregateYearData(String day, String year) {
        try {
            log.info("Aggregating year data for year: {}", year);

            // 计算年的开始和结束日期
            String yearStart = year + "0101";
            String yearEnd = year + "1231";

            // 查询该年的所有日表数据
            List<BusinessAssetDayEntity> dayDataList = businessAssetMapper.selectDayDateByTimeRange(yearStart, yearEnd);

            // 按地区分组汇聚数据
            Map<String, BusinessAssetYearEntity> yearDataMap = new HashMap<>();

            for (BusinessAssetDayEntity dayEntity : dayDataList) {
                String areaId = dayEntity.getAreaId();
                BusinessAssetYearEntity yearEntity = yearDataMap.get(areaId);

                if (yearEntity == null) {
                    yearEntity = new BusinessAssetYearEntity();
                    yearEntity.setId(generateId(areaId, year));
                    yearEntity.setDay(year);
                    yearEntity.setAreaId(areaId);
                    yearEntity.setParentAreaId(dayEntity.getParentAreaId()); // 设置父节点ID
                    yearEntity.setAllNum(0L);
                    yearEntity.setBidNum(0L);
                    yearEntity.setSubBidNum(0L);
                    yearEntity.setBeforeBidNum(0L);
                    yearEntity.setProjectApprovalNum(0L);
                    yearEntity.setProjectAddNum(0L);
                    yearEntity.setProjectStartNum(0L);
                    yearEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    yearDataMap.put(areaId, yearEntity);
                }

                // 汇聚数据
                yearEntity.setAllNum(yearEntity.getAllNum() + (dayEntity.getAllNum() != null ? dayEntity.getAllNum() : 0L));
                yearEntity.setBidNum(yearEntity.getBidNum() + (dayEntity.getBidNum() != null ? dayEntity.getBidNum() : 0L));
                yearEntity.setSubBidNum(yearEntity.getSubBidNum() + (dayEntity.getSubBidNum() != null ? dayEntity.getSubBidNum() : 0L));
                yearEntity.setBeforeBidNum(yearEntity.getBeforeBidNum() + (dayEntity.getBeforeBidNum() != null ? dayEntity.getBeforeBidNum() : 0L));
                yearEntity.setProjectApprovalNum(yearEntity.getProjectApprovalNum() + (dayEntity.getProjectApprovalNum() != null ? dayEntity.getProjectApprovalNum() : 0L));
                yearEntity.setProjectAddNum(yearEntity.getProjectAddNum() + (dayEntity.getProjectAddNum() != null ? dayEntity.getProjectAddNum() : 0L));
                yearEntity.setProjectStartNum(yearEntity.getProjectStartNum() + (dayEntity.getProjectStartNum() != null ? dayEntity.getProjectStartNum() : 0L));
            }

            // 批量插入或更新年表数据
            if (!yearDataMap.isEmpty()) {
                List<BusinessAssetYearEntity> yearDataList = new ArrayList<>(yearDataMap.values());
                businessAssetMapper.batchInsertYearData(yearDataList);
                log.info("Aggregated {} year records for year: {}", yearDataList.size(), year);
            }

        } catch (Exception e) {
            log.error("Error aggregating year data for year: {}", year, e);
        }
    }

    /**
     * 计算周开始日期
     */
    private String calculateWeekStart(String day) {
        try {
            LocalDateTime date = LocalDateTime.parse(day + "000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDateTime weekStart = date.minusDays(date.getDayOfWeek().getValue() - 1);
            return weekStart.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("Error calculating week start for day: {}", day);
            return day;
        }
    }

    /**
     * 计算周结束日期
     */
    private String calculateWeekEnd(String day) {
        try {
            LocalDateTime date = LocalDateTime.parse(day + "000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDateTime weekEnd = date.plusDays(7 - date.getDayOfWeek().getValue());
            return weekEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("Error calculating week end for day: {}", day);
            return day;
        }
    }

    /**
     * 计算月结束日期
     */
    private String calculateMonthEnd(String month) {
        try {
            LocalDateTime date = LocalDateTime.parse(month + "01000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDateTime monthEnd = date.plusMonths(1).minusDays(1);
            return monthEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            log.warn("Error calculating month end for month: {}", month);
            return month + "31";
        }
    }

    @Override
    public void exportBusinessAssetStat(BusinessAssetQueryDto queryDto) {
        log.info("Exporting business asset statistics, queryDto: {}", queryDto);

        try {
            // 获取统计数据
            BusinessAssetStatVo statData = getStatBusiness(queryDto);

            // 转换为导出数据
            List<BusinessAssetExportVo> exportData = convertToExportData(statData.getAreaStatList(), queryDto);

            // 生成文件名
            String fileName = generateExcelFileName(queryDto);

            // 使用EasyExcel导出
            EasyExcel.write(fileName, BusinessAssetExportVo.class)
                    .sheet("商机资产统计")
                    .doWrite(exportData);

            log.info("Business asset statistics export completed, exported {} area records to file: {}",
                    exportData.size(), fileName);

        } catch (Exception e) {
            log.error("Error exporting business asset statistics", e);
            throw new RuntimeException("Export failed: " + e.getMessage());
        }
    }









    /**
     * 转换为导出数据
     */
    private List<BusinessAssetExportVo> convertToExportData(List<BusinessAssetStatVo.AreaStatData> areaStatList,
                                                           BusinessAssetQueryDto queryDto) {
        List<BusinessAssetExportVo> exportData = new ArrayList<>();

        if (areaStatList == null || areaStatList.isEmpty()) {
            log.warn("No area stat data to convert for export");
            return exportData;
        }

        String formattedTime = formatTimePoint(queryDto.getTimePoint(), queryDto.getTimeType());

        for (BusinessAssetStatVo.AreaStatData areaData : areaStatList) {
            BusinessAssetExportVo exportVo = new BusinessAssetExportVo();

            exportVo.setTime(formattedTime);
            exportVo.setAreaName(areaData.getAreaName());
            exportVo.setAllNum(areaData.getAllNum() != null ? areaData.getAllNum() : 0L);
            exportVo.setProjectAddNum(areaData.getProjectAddNum() != null ? areaData.getProjectAddNum() : 0L);
            exportVo.setBidNum(areaData.getBidNum() != null ? areaData.getBidNum() : 0L);
            exportVo.setProjectStartNum(areaData.getProjectStartNum() != null ? areaData.getProjectStartNum() : 0L);
            exportVo.setSubBidNum(areaData.getSubBidNum() != null ? areaData.getSubBidNum() : 0L);
            exportVo.setBeforeBidNum(areaData.getBeforeBidNum() != null ? areaData.getBeforeBidNum() : 0L);

            exportData.add(exportVo);
        }

        log.info("Converted {} area stat records to export data", exportData.size());
        return exportData;
    }

    /**
     * 生成Excel文件名
     */
    private String generateExcelFileName(BusinessAssetQueryDto queryDto) {
        String timeTypeName = getTimeTypeName(queryDto.getTimeType());
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("商机资产统计_%s_%s_%s.xlsx", timeTypeName, queryDto.getTimePoint(), timestamp);
    }

    /**
     * 获取时间类型名称
     */
    private String getTimeTypeName(Integer timeType) {
        switch (timeType) {
            case 1: return "日";
            case 2: return "周";
            case 3: return "月";
            case 4: return "年";
            default: return "未知";
        }
    }

    /**
     * 格式化时间点显示
     */
    private String formatTimePoint(String timePoint, Integer timeType) {
        if (timePoint == null || timePoint.trim().isEmpty()) {
            return "";
        }

        try {
            switch (timeType) {
                case 1: // 天 - timePoint格式：yyyyMMdd
                    if (timePoint.length() == 8) {
                        LocalDate dayTime = LocalDate.parse(timePoint, DateTimeFormatter.ofPattern("yyyyMMdd"));
                        return dayTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    }
                    return timePoint;

                case 2: // 周 - timePoint格式：yyyyww
                    if (timePoint.length() == 6) {
                        String year = timePoint.substring(0, 4);
                        String week = timePoint.substring(4, 6);
                        int weekNum = Integer.parseInt(week);
                        return year + "年第" + weekNum + "周";
                    }
                    return timePoint + "周";

                case 3: // 月 - timePoint格式：yyyyMM
                    if (timePoint.length() == 6) {
                        LocalDate monthTime = LocalDate.parse(timePoint + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                        return monthTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                    }
                    return timePoint;

                case 4: // 年 - timePoint格式：yyyy
                    return timePoint + "年";

                default:
                    return timePoint;
            }
        } catch (Exception e) {
            log.warn("Error formatting time point: {} with timeType: {}", timePoint, timeType, e);
            return timePoint;
        }
    }

    @Override
    public void processBusinessStartBuryingPoint(String projectId, String areaId) {
        log.info("Processing business start burying point, projectId: {}, areaId: {}", projectId, areaId);

        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                log.warn("Project ID is null or empty, skipping burying point processing");
                return;
            }

            if (areaId == null || areaId.trim().isEmpty()) {
                log.warn("Area ID is null or empty, skipping burying point processing");
                return;
            }

            // 获取当前日期
            String currentDay = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 插入启动投标记录
            businessAssetMapper.insertBusinessStartRecord(projectId, areaId, currentDay);

            // 更新当日统计数据
            updateDayStatistics(areaId, currentDay);

            log.info("Business start burying point processed successfully for projectId: {}, areaId: {}", projectId, areaId);

        } catch (Exception e) {
            log.error("Error processing business start burying point for projectId: {}, areaId: {}", projectId, areaId, e);
        }
    }

    /**
     * 更新当日统计数据
     */
    private void updateDayStatistics(String areaId, String day) {
        try {
            // 检查当日数据是否存在
            Integer exists = businessAssetMapper.checkDayDataExists(day, areaId);

            if (exists != null && exists > 0) {
                // 数据存在，更新启动投标数量
                Long startCount = businessAssetMapper.countBusinessStartRecordsByDay(day, areaId);

                // 查询现有数据
                List<BusinessAssetDayEntity> existingData = businessAssetMapper.selectDayDateByAreaIdAndTimeRange(areaId, day, day);
                if (!existingData.isEmpty()) {
                    BusinessAssetDayEntity entity = existingData.get(0);
                    entity.setProjectStartNum(startCount);
                    entity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    businessAssetMapper.updateDayData(entity);
                }
            } else {
                // 数据不存在，创建新记录
                BusinessAssetDayEntity newEntity = new BusinessAssetDayEntity();
                newEntity.setId(UUID.randomUUID().toString());
                newEntity.setDay(day);
                newEntity.setAreaId(areaId);
                newEntity.setAllNum(0L);
                newEntity.setBidNum(0L);
                newEntity.setSubBidNum(0L);
                newEntity.setBeforeBidNum(0L);
                newEntity.setProjectApprovalNum(0L);
                newEntity.setProjectAddNum(0L);
                newEntity.setProjectStartNum(1L); // 新增启动投标
                newEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                List<BusinessAssetDayEntity> insertList = new ArrayList<>();
                insertList.add(newEntity);
                businessAssetMapper.batchInsertDayData(insertList);
            }

        } catch (Exception e) {
            log.error("Error updating day statistics for areaId: {}, day: {}", areaId, day, e);
        }
    }

    /**
     * 生成ID
     */
    private String generateId(String areaId, String day) {
        return areaId + "_" + day + "_" + System.currentTimeMillis();
    }

    @Override
    @Async
    public void processBusinessStartBuryingPointAsync(String projectId, String areaId) {
        log.info("Processing business start burying point asynchronously, projectId: {}, areaId: {}", projectId, areaId);

        try {
            // 异步处理埋点数据
            processBusinessStartBuryingPoint(projectId, areaId);

        } catch (Exception e) {
            log.error("Error processing business start burying point asynchronously", e);
        }
    }



    /**
     * 将从project模块获取的商机统计数据保存到天表
     */
    private void saveBusinessStatisticsToDay(List<BusinessStatisticsVo> areaBusinessStatistics) {
        log.info("Saving {} business statistics records to day table", areaBusinessStatistics.size());

        areaBusinessStatistics = areaBusinessStatistics.stream().filter(vo -> !"root".equals(vo.getAreaId())).collect(Collectors.toList());

        try {
            // 检查天表是否有任何数据
            boolean hasAnyData = hasAnyDataInDayTable();

            if (!hasAnyData) {
                // 天表没数据，分批插入
                batchInsertBusinessStatistics(areaBusinessStatistics);
            } else {
                // 天表有数据，逐条检查并插入或更新
                insertOrUpdateBusinessStatistics(areaBusinessStatistics);
            }

        } catch (Exception e) {
            log.error("Error saving business statistics to day table", e);
        }
    }

    /**
     * 检查天表是否有任何数据
     */
    private boolean hasAnyDataInDayTable() {
        try {
            // 使用COUNT查询检查天表中是否有任何记录（更高效）
            Integer count = businessAssetMapper.countAllDayData();
            boolean hasData = count != null && count > 0;
            log.info("Day table has any data: {} (total count: {})", hasData, count);
            return hasData;
        } catch (Exception e) {
            log.error("Error checking if day table has any data", e);
            return false;
        }
    }

    /**
     * 分批插入商机统计数据（天表无数据时使用）
     */
    private void batchInsertBusinessStatistics(List<BusinessStatisticsVo> areaBusinessStatistics) {
        log.info("Batch inserting {} business statistics records (day table is empty)", areaBusinessStatistics.size());

        final int BATCH_SIZE = 500; // 每批插入100条记录
        List<BusinessAssetDayEntity> currentBatch = new ArrayList<>();
        int totalProcessed = 0;

        try {
            for (BusinessStatisticsVo statistics : areaBusinessStatistics) {
                BusinessAssetDayEntity dayEntity = convertToBusinessAssetDayEntity(statistics);
                currentBatch.add(dayEntity);

                // 达到批次大小或处理完所有数据时执行插入
                if (currentBatch.size() >= BATCH_SIZE || totalProcessed + currentBatch.size() >= areaBusinessStatistics.size()) {
                    businessAssetMapper.batchInsertDayData(currentBatch);
                    log.info("Batch inserted {} records, total processed: {}/{}",
                            currentBatch.size(), totalProcessed + currentBatch.size(), areaBusinessStatistics.size());

                    totalProcessed += currentBatch.size();
                    currentBatch.clear();
                }
            }

            log.info("Successfully batch inserted all {} business statistics records", totalProcessed);

        } catch (Exception e) {
            log.error("Error in batch inserting business statistics", e);
        }
    }

    /**
     * 逐条检查并插入或更新商机统计数据（天表有数据时使用）
     */
    private void insertOrUpdateBusinessStatistics(List<BusinessStatisticsVo> areaBusinessStatistics) {
        log.info("Insert or update {} business statistics records (day table has data)", areaBusinessStatistics.size());

        int insertCount = 0;
        int updateCount = 0;

        try {
            for (BusinessStatisticsVo statistics : areaBusinessStatistics) {
                // 检查该日期和地区ID的数据是否存在
                Integer existsCount = businessAssetMapper.checkDayDataExists(statistics.getDay(), statistics.getAreaId());

                if (existsCount != null && existsCount > 0) {
                    // 数据存在，执行更新
                    BusinessAssetDayEntity updateEntity = convertToBusinessAssetDayEntity(statistics);
                    businessAssetMapper.updateDayData(updateEntity);
                    updateCount++;

                    if (updateCount % 50 == 0) {
                        log.info("Updated {} records so far", updateCount);
                    }
                } else {
                    // 数据不存在，执行插入
                    BusinessAssetDayEntity insertEntity = convertToBusinessAssetDayEntity(statistics);
                    List<BusinessAssetDayEntity> singleRecord = new ArrayList<>();
                    singleRecord.add(insertEntity);
                    businessAssetMapper.batchInsertDayData(singleRecord);
                    insertCount++;

                    if (insertCount % 50 == 0) {
                        log.info("Inserted {} records so far", insertCount);
                    }
                }
            }

            log.info("Successfully processed all records - Inserted: {}, Updated: {}", insertCount, updateCount);

        } catch (Exception e) {
            log.error("Error in insert or update business statistics", e);
        }
    }

    /**
     * 将BusinessStatisticsVo转换为BusinessAssetDayEntity
     */
    private BusinessAssetDayEntity convertToBusinessAssetDayEntity(BusinessStatisticsVo statistics) {
        BusinessAssetDayEntity dayEntity = new BusinessAssetDayEntity();

        // 设置基本信息
        dayEntity.setId(UUID.randomUUID().toString());
        dayEntity.setDay(statistics.getDay());
        dayEntity.setAreaId(statistics.getAreaId());
        dayEntity.setParentAreaId(statistics.getParentAreaId());

        // 设置统计数据
        dayEntity.setAllNum(statistics.getAllNum() != null ? statistics.getAllNum() : 0L);
        dayEntity.setBidNum(statistics.getBidNum() != null ? statistics.getBidNum() : 0L);
        dayEntity.setSubBidNum(statistics.getSubBidNum() != null ? statistics.getSubBidNum() : 0L);
        dayEntity.setBeforeBidNum(statistics.getBeforeBidNum() != null ? statistics.getBeforeBidNum() : 0L);
        dayEntity.setProjectApprovalNum(statistics.getProjectApprovalNum() != null ? statistics.getProjectApprovalNum() : 0L);
        dayEntity.setProjectAddNum(statistics.getProjectAddNum() != null ? statistics.getProjectAddNum() : 0L);
        dayEntity.setProjectStartNum(statistics.getProjectStartNum() != null ? statistics.getProjectStartNum() : 0L);

        // 设置创建时间
        dayEntity.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return dayEntity;
    }
}
