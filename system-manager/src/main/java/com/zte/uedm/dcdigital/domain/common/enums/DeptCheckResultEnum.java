/* Started by AICoder, pid:ba678ve144ydca314e6b099b20a38498d5780fd5 */
package com.zte.uedm.dcdigital.domain.common.enums;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum DeptCheckResultEnum {

    /**
     * 通过
     */
    CHECK_PASS(1, "{\"zh-CN\":\"通过\",\"en-US\":\"Pass\"}"),

    /**
     * 不通过
     */
    CHECK_NOT_PASS(0, "{\"zh-CN\":\"不通过\",\"en-US\":\"Not Pass\"}"),

    /**
     * 姓名不能为空
     */
    NAME_NOT_NULL(2, "{\"zh-CN\":\"姓名不能为空\",\"en-US\":\"Name can not be null\"}"),

    /**
     * 姓名字符长度不能大于50
     */
    NAME_LENGTH_ERROR(3, "{\"zh-CN\":\"姓名字符长度不能大于50\",\"en-US\":\"Name length can not be greater than 50\"}"),

    /**
     * 工号不能为空
     */
    USER_ID_NOT_NULL(4, "{\"zh-CN\":\"工号不能为空\",\"en-US\":\"User ID can not be null\"}"),

    /**
     * 工号长度不能超过13位
     */
    USER_ID_LENGTH_ERROR(5, "{\"zh-CN\":\"工号长度不能大于13\",\"en-US\":\"User ID length can not be greater than 13\"}"),
    /**
     * 工号必须为纯数字
     * */
    USER_ID_NOT_NUMBER(6, "{\"zh-CN\":\"工号必须为纯数字\",\"en-US\":\"User ID must be a number\"}"),

    /**
     * 导入数据不能超过500条
     */
    IMPORT_DATA_OVER(7, "{\"zh-CN\":\"导入总数据不能超过500条\",\"en-US\":\"Import total data can not be greater than 500\"}"),

    /**
     * 用户已存在当前叶子节点
     * */
    USER_EXIST(8, "{\"zh-CN\":\"用户已存在当前叶子节点\",\"en-US\":\"User already exists in the current leaf node\"}"),

    /**
     * 用户已存在当前叶子节点
     * */
    USER_DUPLICATE(9, "{\"zh-CN\":\"导入用户数据重复\",\"en-US\":\"User data duplication\"}"),

    /**
     * 一级部门
     * */
    FIRST_DEPT(10, "{\"zh-CN\":\"一级部门\",\"en-US\":\"First level department\"}"),

    /**
     * 二级部门
     * */
    SECOND_DEPT(11, "{\"zh-CN\":\"二级部门\",\"en-US\":\"Second level department\"}"),

    /**
     * 三级部门
     * */
    THIRD_DEPT(12, "{\"zh-CN\":\"三级部门\",\"en-US\":\"Third level department\"}"),

    /**
     * 四级部门
     * */
    FOUR_DEPT(13, "{\"zh-CN\":\"四级部门\",\"en-US\":\"Fourth level department\"}"),

    /**
     * 用户已存在其他部门
     * */
    USER_EXIST_OTHER_DEPT(14, "{\"zh-CN\":\"用户已存在其他部门\",\"en-US\":\"User already exists in the other department\"}"),
    /**
     * uac系统不存在该用户
     * */
    USER_NOT_FOUND_UAC(15, "{\"zh-CN\":\"uac系统不存在该用户\",\"en-US\":\"This user does not exist in the uac system\"}"),
    DEPARTMENT(16, "{\"zh-CN\":\"科室/团队\",\"en-US\":\"Department\"}");
    private Integer code;
    private String value;

    DeptCheckResultEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public static String getValueByCode(Integer code) {
        for (DeptCheckResultEnum e : DeptCheckResultEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getValue();
            }
        }
        log.error("CustomerTypeEnum.getValueByCode value is null: code:{}", code);
        throw new BusinessException(StatusCode.DATA_NOT_FOUND);
    }
}

/* Ended by AICoder, pid:ba678ve144ydca314e6b099b20a38498d5780fd5 */