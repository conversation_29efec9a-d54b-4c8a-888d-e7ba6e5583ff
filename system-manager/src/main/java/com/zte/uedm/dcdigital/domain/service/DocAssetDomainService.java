package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetAddDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetJuniorQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocAssetQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.Context;
import java.util.List;

public interface DocAssetDomainService {
    /**
     * 文档日数据统计数据同步
     */
    void synchronizeDocAssetDayData();
    /**
     * 文档数据统计数据同步
     */
    void synchronizeDocAssetData();
    /**
     * 文档资产编辑埋点
     */
    void addDocAssetRecord(DocAssetAddDto addDto);
    /**
     * 文档数量统计
     */
    DocAssetNumVo getStatDocument(DocAssetQueryDto queryDto);

    DocAssetJuniorNumVo getStatJuniorStatistics(DocAssetJuniorQueryDto queryDto);

    void export(DocAssetJuniorQueryDto queryDto, HttpServletResponse respons);
}
