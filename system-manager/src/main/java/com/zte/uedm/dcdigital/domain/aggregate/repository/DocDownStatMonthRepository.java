package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatMonthEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatWeekEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatMonthEntity;
import org.springframework.lang.NonNull;

import java.util.List;

public interface DocDownStatMonthRepository {
    /**
     * 批量添加文档下载月统计记录
     *
     * @param monthEntityList 包含多个统计实体的列表，每个实体必须包含：
     *                       - date: 统计日期（yyyyMMdd格式）
     *                       - loginCount: 登录次数
     */
    void addDocDownStatMonthList(@NonNull List<DocDownStatMonthEntity> monthEntityList);

//    /**
//     * 查询用户登录月统计记录
//     *
//     * @param monthEntity 查询条件
//     * @return 对应日期的统计记录列表，可能返回空列表
//     */
//    List<LoginStatMonthEntity> getLoginStatMonthList(LoginStatMonthEntity monthEntity);
//
    /**
     * 批量更新文档下载月统计记录
     *
     * @param monthEntityList 包含更新信息的统计实体列表，每个实体必须包含：
     *                       - date: 统计日期（yyyyMMdd格式）
     *                       - loginCount: 登录次数
     */
    void updateDocDownStatMonthList(@NonNull List<DocDownStatMonthEntity> monthEntityList);
    List<DocDownStatMonthEntity> getDownAndViewNum(DocDownStatMonthEntity monthEntity, List<String> deptUserIds);

    List<DocDownStatMonthEntity> getPreviewTop50Data(DocDownStatMonthEntity monthEntity, List<String> deptUserIds);

    List<DocDownStatMonthEntity> getDownloadTop50Data(DocDownStatMonthEntity monthEntity, List<String> deptUserIds);

    List<DocDownStatMonthEntity> getPreviewDetailsData(DocDownStatMonthEntity monthEntity, List<String> deptUserIds);

    List<DocDownStatMonthEntity> getDownloadDetailsData(DocDownStatMonthEntity monthEntity, List<String> deptUserIds);

    List<DocDownStatEntity> getUserStatisticFileDate(DocDownStatEntity entity, List<String> deptUserIds);
}
