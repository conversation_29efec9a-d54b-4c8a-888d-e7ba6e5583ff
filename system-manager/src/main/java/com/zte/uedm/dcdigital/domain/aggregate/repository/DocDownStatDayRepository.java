package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.DocDownStatEntity;
import com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatDto;

import java.util.List;

public interface DocDownStatDayRepository {
    /**
     * 获取指定日期范围内的文档下载统计数据（包含周、月、年的下载次数、预览次数）
     *
     * @param previousDayNumber 前一天的日期
     * @param previousWeekNumber 前一天是当年的第几周
     * @param previousMonthNumber 前一天是当年的第几个月
     * @param previousYearNumber 前一天所在年份
     * @return 文档下载统计数据列表
     */
    List<DocDownStatDto> getDocDownStatWithDataList(
            Integer previousDayNumber,
            Integer previousWeekNumber,
            Integer previousMonthNumber,
            Integer previousYearNumber
    );

    /**
     * 根据用户id、资源id和时间获取数据
     */
    DocDownStatDayEntity selectByUserIdAndResourceIdAndDay(String userId, String resourceId, int day);

    /**
     * 插入新数据
     */
    void addRecordStatDay(DocDownStatDayEntity dayEntity);

    /**
     *
     * 更新数据
     */
    void updateRecordStatDay(DocDownStatDayEntity dayEntity);

    /**
     * 获得下载和预览的数量
     * @param dayEntity
     * @return
     */
    List<DocDownStatDayEntity> getDownAndViewNum(DocDownStatDayEntity dayEntity, List<String> deptUserId);

    /**
     * 获得预览TOP50数量
     */
    List<DocDownStatDayEntity> getPreviewTop50Data(DocDownStatDayEntity dayEntity, List<String> deptUserIds);

    /**
     * 获得下载TOP50数量
     */
    List<DocDownStatDayEntity> getDownloadTop50Data(DocDownStatDayEntity dayEntity, List<String> deptUserIds);

    List<DocDownStatDayEntity> getPreviewDetailsData(DocDownStatDayEntity dayEntity, List<String> deptUserIds);

    List<DocDownStatDayEntity> getDownloadDetailsData(DocDownStatDayEntity dayEntity, List<String> deptUserIds);

    List<DocDownStatEntity> getUserStatisticFileDate(DocDownStatEntity entity, List<String> deptUserIds);
}
