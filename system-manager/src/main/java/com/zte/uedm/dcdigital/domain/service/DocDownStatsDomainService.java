package com.zte.uedm.dcdigital.domain.service;

import com.zte.uedm.dcdigital.interfaces.web.dto.DocDownStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocDownStatisticsTopVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocDownStatisticsVo;
import com.zte.uedm.dcdigital.interfaces.web.vo.DocDownUserStatisticsVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface DocDownStatsDomainService {
    /**
     * 文档下载统计数据同步
     */
    void synchronizeDocDownStatData();

    void addRecordDocDown(String userId, String resourceId, int operationType);

    DocDownStatisticsVo calculateSystemAccessDocDownStatistic(DocDownStatQueryDto queryDto);

    DocDownStatisticsVo getPreviewTop(DocDownStatQueryDto queryDto);

    DocDownStatisticsVo getDownloadTop(DocDownStatQueryDto queryDto);

    List<DocDownStatisticsTopVo> getStatisticFileTopData(DocDownStatQueryDto queryDto);

    List<DocDownUserStatisticsVo> getUserStatisticFileDate(DocDownStatQueryDto queryDto);

    void export(DocDownStatQueryDto queryDto, HttpServletResponse response);
}
