/* Started by AICoder, pid:d598fl13b1f092e14eb3084560786341836300be */
package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatMonthEntity;
import org.springframework.lang.NonNull;

import java.util.List;

public interface LoginStatMonthRepository {
    /**
     * 添加单条用户登录月统计记录
     *
     * @param monthEntity 包含完整统计信息的实体对象，必须包含：
     *                   - date: 统计日期（yyyyMMdd格式）
     *                   - loginCount: 登录次数
     */
    void addLoginStatMonth(@NonNull LoginStatMonthEntity monthEntity);

    /**
     * 批量添加用户登录月统计记录
     *
     * @param monthEntityList 包含多个统计实体的列表，每个实体必须包含：
     *                       - date: 统计日期（yyyyMMdd格式）
     *                       - loginCount: 登录次数
     */
    void addLoginStatMonthList(@NonNull List<LoginStatMonthEntity> monthEntityList);

    /**
     * 查询用户登录月统计记录
     *
     * @param monthEntity 查询条件
     * @return 对应日期的统计记录列表，可能返回空列表
     */
    List<LoginStatMonthEntity> getLoginStatMonthList(LoginStatMonthEntity monthEntity);

    /**
     * 批量更新用户登录月统计记录
     *
     * @param monthEntityList 包含更新信息的统计实体列表，每个实体必须包含：
     *                       - date: 统计日期（yyyyMMdd格式）
     *                       - loginCount: 登录次数
     */
    void updateLoginStatMonthList(@NonNull List<LoginStatMonthEntity> monthEntityList);

    /**
     *  根据月份删除月表数据
     * */
    void deleteByMonthNumber(Integer monthNumber);
}

/* Ended by AICoder, pid:d598fl13b1f092e14eb3084560786341836300be */