/* Started by AICoder, pid:q7ba7a6a59u697a1470b0bd770b7b4708da099f7 */
package com.zte.uedm.dcdigital.domain.aggregate.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 页面访问周统计数据持久化对象
 */
@Getter
@Setter
@ToString
public class PageStatWeekEntity {
    /**
     * 主键ID（UUID格式）
     */
    private String id;

    /**
     * 统计周（格式：YYYYWW，如202526表示2025年第26周）
     */
    private Integer day;

    /**
     * 部门/组织ID
     */
    private String deptId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 产品类别ID
     */
    private String productCategoryId;

    /**
     * 类型
     */
    private String type;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 访问次数
     */
    private Integer num;

    // 查询条件字段（非数据库字段）
    @TableField(exist = false)
    private Integer beginTime;

    @TableField(exist = false)
    private Integer endTime;
}

/* Ended by AICoder, pid:q7ba7a6a59u697a1470b0bd770b7b4708da099f7 */