package com.zte.uedm.dcdigital.domain.gateway;

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.CategoryDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.MaterialDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.ProductLineDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.dto.SpecModelDto;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.CategoryVo;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.MaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.ProductLineVo;
import com.zte.uedm.dcdigital.interfaces.web.pdm.vo.SpecModelVo;

import java.util.List;

public interface PdmApiService {


    /* Started by AICoder, pid:3a576ne185h6a36146850a3f0076dc0f4e36e6ca */
    /**
     * 获取产品线列表
     * @param dto
     * @return
     */
    PageVO<ProductLineVo> getProductLineList(ProductLineDto dto);
    /* Ended by AICoder, pid:3a576ne185h6a36146850a3f0076dc0f4e36e6ca */

    /**
     * 查询产品分类
     * @param dto
     * @return
     */
    List<CategoryVo> getLargeCategoryList(CategoryDto dto);

    /**
     * 查询PDM规格型号
     * @param dto
     * @return
     */
    PageVO<SpecModelVo> getSpecificationModel(SpecModelDto dto);


    /**
     * 查询pdm物料信息
     * @param dto
     * @return
     */
    PageVO<MaterialVo> getMaterialList(MaterialDto dto);


    /**
     * 根据销售编码查询物料信息
     * @param salesCodeList
     * @return
     */
    List<MaterialVo> getMaterialListByCode(List<String> salesCodeList);

    /**
     * 根据销售编码查询物料成本信息
     * @param salesCodeList
     * @return
     */
    List<MaterialVo> getMaterialListCostByCode(List<String> salesCodeList);
}
