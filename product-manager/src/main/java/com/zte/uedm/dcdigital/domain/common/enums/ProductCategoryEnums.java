/* Started by AICoder, pid:d11c526fa5s48d014a840a1b50c1bb7004e23830 */
package com.zte.uedm.dcdigital.domain.common.enums;

/**
 * 产品分类枚举，定义了产品线、产品大类和产品小类
 * <AUTHOR>
 */

public enum ProductCategoryEnums {

    /**
     * 产品线
     */
    PRODUCT_LINE(1, "{\"zh-CN\":\"产品线\",\"en-US\":\"Product Line\"}"),

    /**
     * 产品大类
     */
    PRODUCT_CATEGORIES(2, "{\"zh-CN\":\"产品大类\",\"en-US\":\"Product Categories\"}"),

    /**
     * 产品小类
     */
    PRODUCT_SUBCATEGORY(3, "{\"zh-CN\":\"产品小类\",\"en-US\":\"Product subcategory\"}");

    private Integer id;
    private String name;

    /**
     * 获取枚举的ID。
     *
     * @return 枚举ID
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * 获取枚举的名称（多语言格式）。
     *
     * @return 枚举名称
     */
    public String getName() {
        return this.name;
    }

    ProductCategoryEnums(Integer id, String name) {
        this.id = id;
        this.name = name;
    }
    /**
     * 根据id获取
     *
     * @param id 枚举id
     * @return 枚举
     */
    public static ProductCategoryEnums getById(Integer id) {
        ProductCategoryEnums[] arr = ProductCategoryEnums.values();
        for (ProductCategoryEnums enu : arr) {
            if (enu.getId().equals(id)) {
                return enu;
            }
        }
        return null;
    }
}
/* Ended by AICoder, pid:d11c526fa5s48d014a840a1b50c1bb7004e23830 */